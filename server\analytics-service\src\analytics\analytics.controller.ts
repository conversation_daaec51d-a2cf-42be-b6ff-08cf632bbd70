import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  HttpStatus, 
  HttpException,
  UseGuards,
  Headers
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import moment from 'moment';
import { AnalyticsService } from './analytics.service';
import { RealtimeAnalyticsService } from './realtime-analytics.service';
import { VisualizationService } from './visualization.service';
import { ReportGenerationService } from './report-generation.service';
import { MachineLearningService } from './machine-learning.service';

/**
 * 分析服务控制器
 */
@ApiTags('Analytics')
@Controller('analytics')
export class AnalyticsController {
  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly realtimeAnalyticsService: RealtimeAnalyticsService,
    private readonly visualizationService: VisualizationService,
    private readonly reportGenerationService: ReportGenerationService,
    private readonly machineLearningService: MachineLearningService
  ) {}

  /**
   * 获取KPI指标
   */
  @Get('kpi')
  @ApiOperation({ summary: '获取KPI指标' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @ApiQuery({ name: 'deviceIds', required: false, description: '设备ID列表' })
  @ApiResponse({ status: 200, description: 'KPI指标获取成功' })
  async getKPIMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('deviceIds') deviceIds?: string
  ) {
    try {
      const start = startDate ? new Date(startDate) : moment().subtract(24, 'hours').toDate();
      const end = endDate ? new Date(endDate) : new Date();
      const devices = deviceIds ? deviceIds.split(',') : undefined;

      const kpiMetrics = await this.analyticsService.calculateKPIMetrics(start, end, devices);
      
      return {
        success: true,
        data: kpiMetrics,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取实时KPI
   */
  @Get('kpi/realtime')
  @ApiOperation({ summary: '获取实时KPI指标' })
  @ApiResponse({ status: 200, description: '实时KPI指标获取成功' })
  async getRealtimeKPI() {
    try {
      const realtimeKPI = await this.realtimeAnalyticsService.getRealtimeKPI();
      
      return {
        success: true,
        data: realtimeKPI,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 执行趋势分析
   */
  @Get('trends/:metric')
  @ApiOperation({ summary: '执行趋势分析' })
  @ApiParam({ name: 'metric', description: '分析指标' })
  @ApiQuery({ name: 'period', required: false, description: '分析周期（天）' })
  @ApiQuery({ name: 'deviceIds', required: false, description: '设备ID列表' })
  @ApiResponse({ status: 200, description: '趋势分析完成' })
  async performTrendAnalysis(
    @Param('metric') metric: string,
    @Query('period') period?: string,
    @Query('deviceIds') deviceIds?: string
  ) {
    try {
      const analysisPeriod = period ? parseInt(period) : 30;
      const devices = deviceIds ? deviceIds.split(',') : undefined;

      const trendAnalysis = await this.analyticsService.performTrendAnalysis(
        metric,
        analysisPeriod,
        devices
      );
      
      return {
        success: true,
        data: trendAnalysis,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 异常检测
   */
  @Get('anomalies')
  @ApiOperation({ summary: '执行异常检测' })
  @ApiQuery({ name: 'deviceIds', required: false, description: '设备ID列表' })
  @ApiQuery({ name: 'sensitivity', required: false, description: '敏感度 (0-1)' })
  @ApiResponse({ status: 200, description: '异常检测完成' })
  async detectAnomalies(
    @Query('deviceIds') deviceIds?: string,
    @Query('sensitivity') sensitivity?: string
  ) {
    try {
      const devices = deviceIds ? deviceIds.split(',') : undefined;
      const sens = sensitivity ? parseFloat(sensitivity) : 0.8;

      const anomalies = await this.analyticsService.detectAnomalies(devices, sens);
      
      return {
        success: true,
        data: anomalies,
        count: anomalies.length,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取活跃警报
   */
  @Get('alerts/active')
  @ApiOperation({ summary: '获取活跃警报' })
  @ApiResponse({ status: 200, description: '活跃警报获取成功' })
  async getActiveAlerts() {
    try {
      const alerts = this.realtimeAnalyticsService.getActiveAlerts();
      
      return {
        success: true,
        data: alerts,
        count: alerts.length,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 确认警报
   */
  @Put('alerts/:alertId/acknowledge')
  @ApiOperation({ summary: '确认警报' })
  @ApiParam({ name: 'alertId', description: '警报ID' })
  @ApiResponse({ status: 200, description: '警报确认成功' })
  async acknowledgeAlert(@Param('alertId') alertId: string) {
    try {
      const success = await this.realtimeAnalyticsService.acknowledgeAlert(alertId);
      
      if (!success) {
        throw new HttpException(
          { success: false, message: '警报不存在或已确认' },
          HttpStatus.NOT_FOUND
        );
      }
      
      return {
        success: true,
        message: '警报已确认',
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 生成生产报告
   */
  @Post('reports/generate')
  @ApiOperation({ summary: '生成生产报告' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reportType: { type: 'string', enum: ['daily', 'weekly', 'monthly'] },
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' },
        options: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '报告生成请求已提交' })
  async generateProductionReport(
    @Body() body: any,
    @Headers('user-id') userId: string = 'anonymous'
  ) {
    try {
      const { reportType, startDate, endDate, options } = body;
      
      const report = await this.analyticsService.generateProductionReport(
        reportType,
        new Date(startDate),
        new Date(endDate),
        options
      );
      
      return {
        success: true,
        data: report,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 生成报表文件
   */
  @Post('reports/file')
  @ApiOperation({ summary: '生成报表文件' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        type: { type: 'string', enum: ['daily', 'weekly', 'monthly', 'custom'] },
        format: { type: 'string', enum: ['pdf', 'excel', 'html', 'json'] },
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' },
        deviceIds: { type: 'array', items: { type: 'string' } },
        includeCharts: { type: 'boolean' },
        includeRawData: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '报表生成请求已提交' })
  async generateReportFile(
    @Body() request: any,
    @Headers('user-id') userId: string = 'anonymous'
  ) {
    try {
      const report = await this.reportGenerationService.generateReport(request, userId);
      
      return {
        success: true,
        data: {
          reportId: report.id,
          status: report.status,
          message: '报表生成请求已提交，请稍后查看结果'
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取报表列表
   */
  @Get('reports')
  @ApiOperation({ summary: '获取报表列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, description: '报表类型' })
  @ApiQuery({ name: 'status', required: false, description: '报表状态' })
  @ApiResponse({ status: 200, description: '报表列表获取成功' })
  async getReports(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('type') type?: string,
    @Query('status') status?: string
  ) {
    try {
      const pageNum = page ? parseInt(page) : 1;
      const limitNum = limit ? parseInt(limit) : 20;
      const filters = { type, status };

      const result = await this.reportGenerationService.getReports(pageNum, limitNum, filters);
      
      return {
        success: true,
        data: result.reports,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.total,
          pages: Math.ceil(result.total / limitNum)
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 下载报表
   */
  @Get('reports/:reportId/download')
  @ApiOperation({ summary: '下载报表' })
  @ApiParam({ name: 'reportId', description: '报表ID' })
  @ApiResponse({ status: 200, description: '报表下载成功' })
  async downloadReport(@Param('reportId') reportId: string) {
    try {
      const result = await this.reportGenerationService.downloadReport(reportId);
      
      return {
        success: true,
        data: {
          filePath: result.filePath,
          fileName: result.fileName
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建图表
   */
  @Post('charts/:chartType')
  @ApiOperation({ summary: '创建图表' })
  @ApiParam({ name: 'chartType', description: '图表类型' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' },
        deviceIds: { type: 'array', items: { type: 'string' } },
        options: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '图表创建成功' })
  async createChart(
    @Param('chartType') chartType: string,
    @Body() body: any
  ) {
    try {
      const { startDate, endDate, deviceIds, options } = body;
      const start = new Date(startDate);
      const end = new Date(endDate);

      let chart;
      switch (chartType) {
        case 'efficiency':
          chart = await this.visualizationService.createProductionEfficiencyChart(
            start, end, deviceIds, options?.type
          );
          break;
        case 'quality':
          chart = await this.visualizationService.createQualityAnalysisChart(
            start, end, deviceIds
          );
          break;
        case 'energy':
          chart = await this.visualizationService.createEnergyConsumptionChart(
            start, end, deviceIds
          );
          break;
        case 'oee':
          chart = await this.visualizationService.createOEEGaugeChart(
            start, end, deviceIds
          );
          break;
        case 'heatmap':
          chart = await this.visualizationService.createHeatmapChart(
            start, end, options?.metric
          );
          break;
        default:
          throw new Error(`不支持的图表类型: ${chartType}`);
      }
      
      return {
        success: true,
        data: chart,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建自定义仪表板
   */
  @Post('dashboards')
  @ApiOperation({ summary: '创建自定义仪表板' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        title: { type: 'string' },
        layout: { type: 'string', enum: ['grid', 'flex', 'masonry'] },
        widgets: { type: 'array' },
        refreshInterval: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '仪表板创建成功' })
  async createCustomDashboard(@Body() config: any) {
    try {
      const dashboard = await this.visualizationService.createCustomDashboard(config);
      
      return {
        success: true,
        data: dashboard,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 训练ML模型
   */
  @Post('ml/models/train')
  @ApiOperation({ summary: '训练机器学习模型' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        modelType: { type: 'string', enum: ['regression', 'classification', 'clustering'] },
        algorithm: { type: 'string' },
        features: { type: 'array', items: { type: 'string' } },
        target: { type: 'string' },
        validationSplit: { type: 'number' },
        hyperparameters: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '模型训练已开始' })
  async trainMLModel(@Body() config: any) {
    try {
      // 获取训练数据
      const endDate = new Date();
      const startDate = moment(endDate).subtract(30, 'days').toDate();
      const trainingData = await this.getTrainingData(startDate, endDate);

      const fullConfig = {
        ...config,
        trainingData,
        validationSplit: config.validationSplit || 0.2
      };

      const model = await this.machineLearningService.trainPredictionModel(fullConfig);
      
      return {
        success: true,
        data: {
          modelId: model.id,
          name: model.name,
          algorithm: model.algorithm,
          accuracy: model.accuracy,
          trainedAt: model.trainedAt
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 执行预测
   */
  @Post('ml/models/:modelId/predict')
  @ApiOperation({ summary: '执行预测' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        input: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '预测完成' })
  async predict(
    @Param('modelId') modelId: string,
    @Body() body: any
  ) {
    try {
      const prediction = await this.machineLearningService.predict(modelId, body.input);
      
      return {
        success: true,
        data: prediction,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取ML模型列表
   */
  @Get('ml/models')
  @ApiOperation({ summary: '获取机器学习模型列表' })
  @ApiResponse({ status: 200, description: '模型列表获取成功' })
  async getMLModels() {
    try {
      const models = this.machineLearningService.getModels();
      
      return {
        success: true,
        data: models,
        count: models.length,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // 私有辅助方法

  /**
   * 获取训练数据
   */
  private async getTrainingData(startDate: Date, endDate: Date): Promise<any[]> {
    // 模拟数据生成
    const data: any[] = [];
    const devices = ['device-001', 'device-002', 'device-003'];

    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      devices.forEach(deviceId => {
        data.push({
          timestamp: current.toDate(),
          deviceId,
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5,
          energyConsumption: 50 + Math.random() * 20
        });
      });
      current.add(1, 'hour');
    }

    return data;
  }
}
