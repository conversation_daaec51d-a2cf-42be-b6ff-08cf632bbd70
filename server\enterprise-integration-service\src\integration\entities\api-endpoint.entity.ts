import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * API端点实体
 */
@Entity('api_endpoints')
@Index(['path', 'method'])
@Index(['systemId', 'status'])
export class APIEndpointEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, comment: '系统ID' })
  systemId: string;

  @Column({ type: 'varchar', length: 200, comment: 'API路径' })
  path: string;

  @Column({ type: 'varchar', length: 20, comment: 'HTTP方法' })
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

  @Column({ type: 'varchar', length: 200, comment: 'API名称' })
  name: string;

  @Column({ type: 'text', nullable: true, comment: 'API描述' })
  description?: string;

  @Column({ type: 'varchar', length: 100, comment: '目标系统' })
  targetSystem: string;

  @Column({ type: 'varchar', length: 200, comment: '目标URL' })
  targetUrl: string;

  @Column({ type: 'json', nullable: true, comment: '请求头配置' })
  headers?: any;

  @Column({ type: 'json', nullable: true, comment: '查询参数配置' })
  queryParams?: any;

  @Column({ type: 'json', nullable: true, comment: '请求体映射' })
  requestMapping?: any;

  @Column({ type: 'json', nullable: true, comment: '响应映射' })
  responseMapping?: any;

  @Column({ type: 'varchar', length: 50, comment: '认证类型' })
  authType: 'none' | 'basic' | 'bearer' | 'api_key' | 'oauth2';

  @Column({ type: 'json', nullable: true, comment: '认证配置' })
  authConfig?: any;

  @Column({ type: 'varchar', length: 50, comment: '状态' })
  status: 'active' | 'inactive' | 'deprecated';

  @Column({ type: 'int', default: 0, comment: '调用次数' })
  callCount: number;

  @Column({ type: 'int', default: 0, comment: '成功次数' })
  successCount: number;

  @Column({ type: 'int', default: 0, comment: '失败次数' })
  errorCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, comment: '平均响应时间(ms)' })
  avgResponseTime: number;

  @Column({ type: 'timestamp', nullable: true, comment: '最后调用时间' })
  lastCalledAt?: Date;

  @Column({ type: 'json', nullable: true, comment: '限流配置' })
  rateLimitConfig?: any;

  @Column({ type: 'json', nullable: true, comment: '缓存配置' })
  cacheConfig?: any;

  @Column({ type: 'boolean', default: true, comment: '是否启用日志' })
  loggingEnabled: boolean;

  @Column({ type: 'boolean', default: false, comment: '是否启用监控' })
  monitoringEnabled: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '创建者' })
  createdBy?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '更新者' })
  updatedBy?: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  notes?: string;
}
