/**
 * 地理编码服务
 */
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import * as turf from '@turf/turf';

export interface GeocodingResult {
  display_name: string;
  lat: number;
  lon: number;
  boundingbox: [string, string, string, string];
  class: string;
  type: string;
  importance?: number;
  address?: {
    country?: string;
    state?: string;
    city?: string;
    county?: string;
    town?: string;
    village?: string;
    road?: string;
    house_number?: string;
    postcode?: string;
  };
}

export interface BatchGeocodingRequest {
  addresses: string[];
  options?: {
    limit?: number;
    countrycodes?: string;
    viewbox?: string;
    bounded?: boolean;
  };
}

@Injectable()
export class GeocodingService {
  private readonly logger = new Logger(GeocodingService.name);
  private readonly nominatimBaseUrl = 'https://nominatim.openstreetmap.org';
  private readonly requestDelay = 1000; // Nominatim要求请求间隔至少1秒

  constructor(
    private configService: ConfigService,
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
  ) {}

  /**
   * 地理编码搜索
   */
  async search(query: string, limit: number = 10, bbox?: string, countrycodes?: string): Promise<{ success: boolean; results: GeocodingResult[]; query: string; limit: number }> {
    this.logger.log(`地理编码搜索: ${query}`);

    try {
      // 首先尝试本地数据库搜索
      const localResults = await this.searchLocal(query, limit);

      if (localResults.length > 0) {
        this.logger.log(`从本地数据库找到 ${localResults.length} 个结果`);
        return {
          success: true,
          results: localResults,
          query,
          limit
        };
      }

      // 如果本地没有结果，使用外部地理编码服务
      const externalResults = await this.searchExternal(query, limit, bbox, countrycodes);

      return {
        success: true,
        results: externalResults,
        query,
        limit
      };

    } catch (error) {
      this.logger.error(`地理编码搜索失败: ${error.message}`);
      throw new HttpException(
        `地理编码搜索失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 反向地理编码
   */
  async reverse(lat: number, lon: number, zoom?: number): Promise<{ success: boolean; result: GeocodingResult }> {
    this.logger.log(`反向地理编码: ${lat}, ${lon}`);

    try {
      // 验证坐标范围
      if (lat < -90 || lat > 90 || lon < -180 || lon > 180) {
        throw new Error('坐标超出有效范围');
      }

      // 首先尝试本地数据库搜索
      const localResult = await this.reverseLocal(lat, lon);

      if (localResult) {
        this.logger.log('从本地数据库找到反向地理编码结果');
        return {
          success: true,
          result: localResult
        };
      }

      // 如果本地没有结果，使用外部地理编码服务
      const externalResult = await this.reverseExternal(lat, lon, zoom);

      return {
        success: true,
        result: externalResult
      };

    } catch (error) {
      this.logger.error(`反向地理编码失败: ${error.message}`);
      throw new HttpException(
        `反向地理编码失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 批量地理编码
   */
  async batchGeocode(request: BatchGeocodingRequest): Promise<{ success: boolean; results: Array<{ address: string; results: GeocodingResult[] }> }> {
    this.logger.log(`批量地理编码: ${request.addresses.length} 个地址`);

    const results = [];

    for (const address of request.addresses) {
      try {
        // 添加请求延迟以遵守API限制
        await this.delay(this.requestDelay);

        const searchResult = await this.search(
          address,
          request.options?.limit || 1,
          request.options?.viewbox,
          request.options?.countrycodes
        );

        results.push({
          address,
          results: searchResult.results
        });

      } catch (error) {
        this.logger.error(`地址 "${address}" 地理编码失败: ${error.message}`);
        results.push({
          address,
          results: []
        });
      }
    }

    return {
      success: true,
      results
    };
  }

  /**
   * 地址标准化
   */
  async standardizeAddress(address: string): Promise<{ success: boolean; standardized: string; components: any }> {
    this.logger.log(`地址标准化: ${address}`);

    try {
      const searchResult = await this.search(address, 1);

      if (searchResult.results.length === 0) {
        throw new Error('未找到匹配的地址');
      }

      const result = searchResult.results[0];

      return {
        success: true,
        standardized: result.display_name,
        components: result.address || {}
      };

    } catch (error) {
      this.logger.error(`地址标准化失败: ${error.message}`);
      throw new HttpException(
        `地址标准化失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取地理编码统计信息
   */
  async getGeocodingStats(): Promise<any> {
    return {
      localFeatureCount: await this.spatialFeatureRepository.count(),
      supportedCountries: ['CN', 'US', 'GB', 'DE', 'FR', 'JP'],
      supportedLanguages: ['zh', 'en', 'de', 'fr', 'ja'],
      maxBatchSize: 100,
      rateLimit: '1 request/second'
    };
  }

  // 私有方法

  /**
   * 本地数据库搜索
   */
  private async searchLocal(query: string, limit: number): Promise<GeocodingResult[]> {
    const features = await this.spatialFeatureRepository
      .createQueryBuilder('feature')
      .where('feature.name ILIKE :query', { query: `%${query}%` })
      .orWhere('feature.properties::text ILIKE :query', { query: `%${query}%` })
      .limit(limit)
      .getMany();

    return features.map(feature => this.featureToGeocodingResult(feature));
  }

  /**
   * 本地反向地理编码
   */
  private async reverseLocal(lat: number, lon: number): Promise<GeocodingResult | null> {
    const point = turf.point([lon, lat]);

    // 查找最近的要素
    const features = await this.spatialFeatureRepository
      .createQueryBuilder('feature')
      .where('ST_DWithin(feature.geometry, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326), 0.01)', { lon, lat })
      .orderBy('ST_Distance(feature.geometry, ST_SetSRID(ST_MakePoint(:lon, :lat), 4326))')
      .limit(1)
      .getMany();

    if (features.length === 0) {
      return null;
    }

    return this.featureToGeocodingResult(features[0]);
  }

  /**
   * 外部地理编码搜索
   */
  private async searchExternal(query: string, limit: number, bbox?: string, countrycodes?: string): Promise<GeocodingResult[]> {
    // 这里应该调用外部地理编码API（如Nominatim、Google Geocoding等）
    // 简化实现，返回模拟数据
    const mockResults: GeocodingResult[] = [
      {
        display_name: `搜索结果: ${query}`,
        lat: 39.9042 + Math.random() * 0.1,
        lon: 116.4074 + Math.random() * 0.1,
        boundingbox: ['39.9', '39.91', '116.4', '116.41'],
        class: 'place',
        type: 'city',
        importance: 0.8,
        address: {
          country: '中国',
          state: '北京市',
          city: '北京市',
          road: '示例路'
        }
      }
    ];

    return mockResults.slice(0, limit);
  }

  /**
   * 外部反向地理编码
   */
  private async reverseExternal(lat: number, lon: number, zoom?: number): Promise<GeocodingResult> {
    // 这里应该调用外部反向地理编码API
    // 简化实现，返回模拟数据
    return {
      display_name: `位置 ${lat.toFixed(4)}, ${lon.toFixed(4)}`,
      lat,
      lon,
      boundingbox: [
        (lat - 0.01).toString(),
        (lat + 0.01).toString(),
        (lon - 0.01).toString(),
        (lon + 0.01).toString()
      ],
      class: 'place',
      type: 'location',
      importance: 0.5,
      address: {
        country: '中国',
        state: '示例省',
        city: '示例市',
        road: '示例路'
      }
    };
  }

  /**
   * 要素转换为地理编码结果
   */
  private featureToGeocodingResult(feature: SpatialFeature): GeocodingResult {
    const geometry = JSON.parse(feature.geometry);
    const centroid = turf.centroid(geometry);
    const [lon, lat] = centroid.geometry.coordinates;
    const bbox = turf.bbox(geometry);

    return {
      display_name: feature.name,
      lat,
      lon,
      boundingbox: [bbox[1].toString(), bbox[3].toString(), bbox[0].toString(), bbox[2].toString()],
      class: feature.featureType.toLowerCase(),
      type: feature.featureType.toLowerCase(),
      importance: 0.7,
      address: feature.properties?.address || {}
    };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
