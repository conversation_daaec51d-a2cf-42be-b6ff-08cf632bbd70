import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import moment from 'moment';
import { EnterpriseSystemEntity } from '../entities/enterprise-system.entity';
import { SyncTaskEntity } from '../entities/sync-task.entity';
import { SyncErrorEntity } from '../entities/sync-error.entity';

/**
 * ERP系统配置接口
 */
interface ERPSystemConfig {
  systemId: string;
  name: string;
  type: 'SAP' | 'Oracle' | 'Microsoft_Dynamics' | 'NetSuite' | 'Odoo' | 'Custom';
  baseUrl: string;
  apiVersion?: string;
  authentication: {
    type: 'basic' | 'oauth2' | 'api_key' | 'saml';
    credentials: any;
  };
  endpoints: {
    [key: string]: string;
  };
  dataMapping: {
    [entity: string]: any;
  };
}

/**
 * 同步配置接口
 */
interface SyncConfig {
  entity: string;
  direction: 'import' | 'export' | 'bidirectional';
  schedule: string;
  batchSize: number;
  filters?: any;
  transformations?: any[];
}

/**
 * 数据实体接口
 */
interface ERPDataEntity {
  id: string;
  type: 'customer' | 'supplier' | 'product' | 'order' | 'invoice' | 'inventory' | 'employee';
  data: any;
  lastModified: Date;
  source: string;
}

/**
 * ERP集成服务
 */
@Injectable()
export class ERPIntegrationService {
  private readonly logger = new Logger(ERPIntegrationService.name);
  
  // ERP系统客户端缓存
  private readonly erpClients: Map<string, AxiosInstance> = new Map();
  
  // 同步配置缓存
  private readonly syncConfigs: Map<string, SyncConfig[]> = new Map();

  constructor(
    @InjectRepository(EnterpriseSystemEntity)
    private enterpriseSystemRepository: Repository<EnterpriseSystemEntity>,
    @InjectRepository(SyncTaskEntity)
    private syncTaskRepository: Repository<SyncTaskEntity>,
    @InjectRepository(SyncErrorEntity)
    private syncErrorRepository: Repository<SyncErrorEntity>
  ) {
    this.initializeERPSystems();
  }

  /**
   * 配置ERP系统
   */
  async configureERPSystem(config: ERPSystemConfig): Promise<EnterpriseSystemEntity> {
    try {
      this.logger.log(`配置ERP系统: ${config.name}`);

      // 创建或更新系统配置
      let system = await this.enterpriseSystemRepository.findOne({
        where: { systemId: config.systemId }
      });

      if (system) {
        system.name = config.name;
        system.systemType = config.type;
        system.connectionConfig = config;
        system.status = 'active';
      } else {
        system = this.enterpriseSystemRepository.create({
          systemId: config.systemId,
          name: config.name,
          systemType: config.type,
          connectionConfig: config,
          status: 'active',
          capabilities: ['data_sync', 'real_time', 'batch_processing']
        });
      }

      const savedSystem = await this.enterpriseSystemRepository.save(system);

      // 创建HTTP客户端
      await this.createERPClient(config);

      // 测试连接
      const connectionTest = await this.testERPConnection(config.systemId);
      if (!connectionTest.success) {
        throw new Error(`ERP系统连接测试失败: ${connectionTest.error}`);
      }

      this.logger.log(`ERP系统配置完成: ${config.name}`);
      return savedSystem;

    } catch (error) {
      this.logger.error('配置ERP系统失败', error);
      throw error;
    }
  }

  /**
   * 同步客户数据
   */
  async syncCustomers(systemId: string, direction: 'import' | 'export' = 'import'): Promise<any> {
    try {
      this.logger.log(`开始同步客户数据: ${systemId} (${direction})`);

      const syncTask = await this.createSyncTask(systemId, 'customer', direction);
      
      try {
        let result;
        if (direction === 'import') {
          result = await this.importCustomersFromERP(systemId);
        } else {
          result = await this.exportCustomersToERP(systemId);
        }

        await this.completeSyncTask(syncTask.id, result);
        this.logger.log(`客户数据同步完成: ${result.processedCount} 条记录`);
        
        return result;

      } catch (error) {
        await this.failSyncTask(syncTask.id, error.message);
        throw error;
      }

    } catch (error) {
      this.logger.error('同步客户数据失败', error);
      throw error;
    }
  }

  /**
   * 同步产品数据
   */
  async syncProducts(systemId: string, direction: 'import' | 'export' = 'import'): Promise<any> {
    try {
      this.logger.log(`开始同步产品数据: ${systemId} (${direction})`);

      const syncTask = await this.createSyncTask(systemId, 'product', direction);
      
      try {
        let result;
        if (direction === 'import') {
          result = await this.importProductsFromERP(systemId);
        } else {
          result = await this.exportProductsToERP(systemId);
        }

        await this.completeSyncTask(syncTask.id, result);
        this.logger.log(`产品数据同步完成: ${result.processedCount} 条记录`);
        
        return result;

      } catch (error) {
        await this.failSyncTask(syncTask.id, error.message);
        throw error;
      }

    } catch (error) {
      this.logger.error('同步产品数据失败', error);
      throw error;
    }
  }

  /**
   * 同步订单数据
   */
  async syncOrders(systemId: string, direction: 'import' | 'export' = 'import'): Promise<any> {
    try {
      this.logger.log(`开始同步订单数据: ${systemId} (${direction})`);

      const syncTask = await this.createSyncTask(systemId, 'order', direction);
      
      try {
        let result;
        if (direction === 'import') {
          result = await this.importOrdersFromERP(systemId);
        } else {
          result = await this.exportOrdersToERP(systemId);
        }

        await this.completeSyncTask(syncTask.id, result);
        this.logger.log(`订单数据同步完成: ${result.processedCount} 条记录`);
        
        return result;

      } catch (error) {
        await this.failSyncTask(syncTask.id, error.message);
        throw error;
      }

    } catch (error) {
      this.logger.error('同步订单数据失败', error);
      throw error;
    }
  }

  /**
   * 同步库存数据
   */
  async syncInventory(systemId: string, direction: 'import' | 'export' = 'import'): Promise<any> {
    try {
      this.logger.log(`开始同步库存数据: ${systemId} (${direction})`);

      const syncTask = await this.createSyncTask(systemId, 'inventory', direction);
      
      try {
        let result;
        if (direction === 'import') {
          result = await this.importInventoryFromERP(systemId);
        } else {
          result = await this.exportInventoryToERP(systemId);
        }

        await this.completeSyncTask(syncTask.id, result);
        this.logger.log(`库存数据同步完成: ${result.processedCount} 条记录`);
        
        return result;

      } catch (error) {
        await this.failSyncTask(syncTask.id, error.message);
        throw error;
      }

    } catch (error) {
      this.logger.error('同步库存数据失败', error);
      throw error;
    }
  }

  /**
   * 实时数据推送
   */
  async pushRealtimeData(systemId: string, entityType: string, data: any): Promise<boolean> {
    try {
      const client = this.erpClients.get(systemId);
      if (!client) {
        throw new Error(`ERP系统客户端不存在: ${systemId}`);
      }

      const system = await this.enterpriseSystemRepository.findOne({
        where: { systemId }
      });

      if (!system) {
        throw new Error(`ERP系统配置不存在: ${systemId}`);
      }

      const config = system.connectionConfig as ERPSystemConfig;
      const endpoint = config.endpoints[`push_${entityType}`];

      if (!endpoint) {
        throw new Error(`推送端点不存在: push_${entityType}`);
      }

      // 数据转换
      const transformedData = this.transformDataForERP(data, entityType, config);

      // 发送数据
      const response = await client.post(endpoint, transformedData);

      this.logger.log(`实时数据推送成功: ${systemId} - ${entityType}`);
      return response.status >= 200 && response.status < 300;

    } catch (error) {
      this.logger.error('实时数据推送失败', error);
      return false;
    }
  }

  /**
   * 获取ERP系统状态
   */
  async getERPSystemStatus(systemId: string): Promise<any> {
    try {
      const system = await this.enterpriseSystemRepository.findOne({
        where: { systemId }
      });

      if (!system) {
        throw new Error(`ERP系统不存在: ${systemId}`);
      }

      const connectionTest = await this.testERPConnection(systemId);
      
      // 获取最近的同步任务
      const recentTasks = await this.syncTaskRepository.find({
        where: { systemId },
        order: { createdAt: 'DESC' },
        take: 10
      });

      // 获取错误统计
      const errorCount = await this.syncErrorRepository.count({
        where: { 
          systemId,
          createdAt: moment().subtract(24, 'hours').toDate()
        }
      });

      return {
        systemId,
        name: system.name,
        type: system.systemType,
        status: system.status,
        connectionStatus: connectionTest.success ? 'connected' : 'disconnected',
        lastSyncAt: recentTasks.length > 0 ? recentTasks[0].createdAt : null,
        recentTasks: recentTasks.map(task => ({
          id: task.id,
          entity: task.entityType,
          direction: task.syncDirection,
          status: task.status,
          processedCount: task.processedCount,
          createdAt: task.createdAt
        })),
        errorCount24h: errorCount,
        capabilities: system.capabilities
      };

    } catch (error) {
      this.logger.error('获取ERP系统状态失败', error);
      throw error;
    }
  }

  /**
   * 定期同步任务
   */
  @Cron(CronExpression.EVERY_HOUR)
  async scheduledSync(): Promise<void> {
    try {
      this.logger.log('开始定期同步任务');

      const activeSystems = await this.enterpriseSystemRepository.find({
        where: { status: 'active' }
      });

      for (const system of activeSystems) {
        try {
          const configs = this.syncConfigs.get(system.systemId) || [];
          
          for (const config of configs) {
            if (this.shouldRunSync(config)) {
              await this.runScheduledSync(system.systemId, config);
            }
          }

        } catch (error) {
          this.logger.error(`定期同步失败: ${system.systemId}`, error);
        }
      }

      this.logger.log('定期同步任务完成');

    } catch (error) {
      this.logger.error('定期同步任务失败', error);
    }
  }

  // 私有方法

  /**
   * 初始化ERP系统
   */
  private async initializeERPSystems(): Promise<void> {
    try {
      const systems = await this.enterpriseSystemRepository.find({
        where: { status: 'active' }
      });

      for (const system of systems) {
        if (system.connectionConfig) {
          await this.createERPClient(system.connectionConfig as ERPSystemConfig);
        }
      }

      this.logger.log(`已初始化 ${systems.length} 个ERP系统`);

    } catch (error) {
      this.logger.error('初始化ERP系统失败', error);
    }
  }

  /**
   * 创建ERP客户端
   */
  private async createERPClient(config: ERPSystemConfig): Promise<void> {
    const client = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SmartFactory-Integration/1.0'
      }
    });

    // 配置认证
    this.configureAuthentication(client, config.authentication);

    // 配置请求拦截器
    client.interceptors.request.use(
      (config) => {
        this.logger.debug(`ERP请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('ERP请求错误', error);
        return Promise.reject(error);
      }
    );

    // 配置响应拦截器
    client.interceptors.response.use(
      (response) => {
        this.logger.debug(`ERP响应: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        this.logger.error('ERP响应错误', error);
        return Promise.reject(error);
      }
    );

    this.erpClients.set(config.systemId, client);
  }

  /**
   * 配置认证
   */
  private configureAuthentication(client: AxiosInstance, auth: any): void {
    switch (auth.type) {
      case 'basic':
        client.defaults.auth = {
          username: auth.credentials.username,
          password: auth.credentials.password
        };
        break;
      case 'api_key':
        client.defaults.headers.common[auth.credentials.headerName || 'X-API-Key'] = auth.credentials.apiKey;
        break;
      case 'oauth2':
        client.defaults.headers.common['Authorization'] = `Bearer ${auth.credentials.accessToken}`;
        break;
    }
  }

  /**
   * 测试ERP连接
   */
  private async testERPConnection(systemId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const client = this.erpClients.get(systemId);
      if (!client) {
        return { success: false, error: '客户端不存在' };
      }

      // 发送健康检查请求
      const response = await client.get('/health', { timeout: 5000 });
      return { success: response.status === 200 };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建同步任务
   */
  private async createSyncTask(systemId: string, entityType: string, direction: string): Promise<SyncTaskEntity> {
    const task = this.syncTaskRepository.create({
      systemId,
      entityType,
      syncDirection: direction as any,
      status: 'running',
      startTime: new Date(),
      processedCount: 0,
      errorCount: 0
    });

    return await this.syncTaskRepository.save(task);
  }

  /**
   * 完成同步任务
   */
  private async completeSyncTask(taskId: string, result: any): Promise<void> {
    await this.syncTaskRepository.update(taskId, {
      status: 'completed',
      endTime: new Date(),
      processedCount: result.processedCount,
      errorCount: result.errorCount,
      result: result
    });
  }

  /**
   * 失败同步任务
   */
  private async failSyncTask(taskId: string, errorMessage: string): Promise<void> {
    await this.syncTaskRepository.update(taskId, {
      status: 'failed',
      endTime: new Date(),
      errorMessage
    });
  }

  /**
   * 从ERP导入客户数据
   */
  private async importCustomersFromERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 100,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 向ERP导出客户数据
   */
  private async exportCustomersToERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 50,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 从ERP导入产品数据
   */
  private async importProductsFromERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 200,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 向ERP导出产品数据
   */
  private async exportProductsToERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 150,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 从ERP导入订单数据
   */
  private async importOrdersFromERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 75,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 向ERP导出订单数据
   */
  private async exportOrdersToERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 60,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 从ERP导入库存数据
   */
  private async importInventoryFromERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 300,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 向ERP导出库存数据
   */
  private async exportInventoryToERP(systemId: string): Promise<any> {
    // 模拟实现
    return {
      processedCount: 250,
      errorCount: 0,
      data: []
    };
  }

  /**
   * 转换数据格式
   */
  private transformDataForERP(data: any, entityType: string, config: ERPSystemConfig): any {
    const mapping = config.dataMapping[entityType];
    if (!mapping) {
      return data;
    }

    // 简化的数据转换逻辑
    const transformed: any = {};
    Object.entries(mapping).forEach(([erpField, localField]) => {
      if (data[localField as string] !== undefined) {
        transformed[erpField] = data[localField as string];
      }
    });

    return transformed;
  }

  /**
   * 检查是否应该运行同步
   */
  private shouldRunSync(config: SyncConfig): boolean {
    // 简化的调度检查逻辑
    return true;
  }

  /**
   * 运行计划同步
   */
  private async runScheduledSync(systemId: string, config: SyncConfig): Promise<void> {
    try {
      switch (config.entity) {
        case 'customer':
          await this.syncCustomers(systemId, config.direction);
          break;
        case 'product':
          await this.syncProducts(systemId, config.direction);
          break;
        case 'order':
          await this.syncOrders(systemId, config.direction);
          break;
        case 'inventory':
          await this.syncInventory(systemId, config.direction);
          break;
      }
    } catch (error) {
      this.logger.error(`计划同步失败: ${systemId} - ${config.entity}`, error);
    }
  }
}
