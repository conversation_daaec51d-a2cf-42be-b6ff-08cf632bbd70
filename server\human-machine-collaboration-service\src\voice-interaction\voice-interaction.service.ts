import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { VoiceCommand } from '../database/entities/voice-command.entity';
import { SpeechRecognitionService } from './speech-recognition.service';
import { TextToSpeechService } from './text-to-speech.service';
import { VoiceCommandProcessor } from './voice-command-processor.service';

/**
 * 语音交互服务
 * 统一管理语音识别、合成和指令处理
 */
@Injectable()
export class VoiceInteractionService {
  private readonly logger = new Logger(VoiceInteractionService.name);

  constructor(
    @InjectRepository(VoiceCommand)
    private readonly voiceCommandRepository: Repository<VoiceCommand>,
    private readonly speechRecognitionService: SpeechRecognitionService,
    private readonly textToSpeechService: TextToSpeechService,
    private readonly voiceCommandProcessor: VoiceCommandProcessor,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理语音输入
   * @param audioData 音频数据
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 处理结果
   */
  async processVoiceInput(
    audioData: Buffer | string,
    userId: string,
    sessionId?: string,
  ): Promise<any> {
    try {
      const startTime = Date.now();

      // 1. 语音识别
      const recognitionResult = await this.speechRecognitionService.recognize(audioData);
      
      if (!recognitionResult.success) {
        return {
          success: false,
          error: '语音识别失败',
          details: recognitionResult.error,
        };
      }

      // 2. 指令处理
      const commandResult = await this.voiceCommandProcessor.processCommand(
        recognitionResult.text,
        userId,
        sessionId,
      );

      // 3. 生成语音响应
      let audioResponse = null;
      if (commandResult.response) {
        audioResponse = await this.textToSpeechService.synthesize(
          commandResult.response,
          {
            language: recognitionResult.language || 'zh-CN',
            voice: this.configService.get('TTS_VOICE', 'zh-CN-XiaoxiaoNeural'),
          },
        );
      }

      const processingTime = Date.now() - startTime;

      // 4. 保存语音指令记录
      const voiceCommand = await this.saveVoiceCommand({
        userId,
        sessionId,
        originalAudio: typeof audioData === 'string' ? audioData : audioData.toString('base64'),
        recognizedText: recognitionResult.text,
        recognitionConfidence: recognitionResult.confidence,
        commandType: commandResult.type,
        extractedCommand: commandResult.command,
        parameters: commandResult.parameters,
        success: commandResult.success,
        response: commandResult.response,
        processingTime,
        language: recognitionResult.language,
        audioMetadata: recognitionResult.metadata,
        errorInfo: commandResult.error,
      });

      return {
        success: true,
        id: voiceCommand.id,
        recognizedText: recognitionResult.text,
        confidence: recognitionResult.confidence,
        command: commandResult.command,
        response: commandResult.response,
        audioResponse: audioResponse?.audioData,
        processingTime,
        suggestions: commandResult.suggestions,
      };

    } catch (error) {
      this.logger.error('处理语音输入失败:', error);
      throw error;
    }
  }

  /**
   * 文本转语音
   * @param text 文本内容
   * @param options 合成选项
   * @returns 音频数据
   */
  async textToSpeech(text: string, options?: any): Promise<any> {
    try {
      return await this.textToSpeechService.synthesize(text, {
        language: options?.language || 'zh-CN',
        voice: options?.voice || this.configService.get('TTS_VOICE'),
        speed: options?.speed || 1.0,
        pitch: options?.pitch || 1.0,
        volume: options?.volume || 1.0,
      });
    } catch (error) {
      this.logger.error('文本转语音失败:', error);
      throw error;
    }
  }

  /**
   * 语音转文本
   * @param audioData 音频数据
   * @param options 识别选项
   * @returns 识别结果
   */
  async speechToText(audioData: Buffer | string, options?: any): Promise<any> {
    try {
      return await this.speechRecognitionService.recognize(audioData, {
        language: options?.language || 'zh-CN',
        enablePunctuation: options?.enablePunctuation !== false,
        enableWordTimeOffsets: options?.enableWordTimeOffsets || false,
      });
    } catch (error) {
      this.logger.error('语音转文本失败:', error);
      throw error;
    }
  }

  /**
   * 获取支持的语言列表
   */
  async getSupportedLanguages(): Promise<any> {
    try {
      const speechLanguages = await this.speechRecognitionService.getSupportedLanguages();
      const ttsLanguages = await this.textToSpeechService.getSupportedLanguages();

      return {
        speechRecognition: speechLanguages,
        textToSpeech: ttsLanguages,
        common: speechLanguages.filter(lang => 
          ttsLanguages.some(ttsLang => ttsLang.code === lang.code)
        ),
      };
    } catch (error) {
      this.logger.error('获取支持语言失败:', error);
      throw error;
    }
  }

  /**
   * 获取语音指令历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 指令历史
   */
  async getVoiceCommandHistory(userId: string, limit: number = 20): Promise<VoiceCommand[]> {
    try {
      return await this.voiceCommandRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error('获取语音指令历史失败:', error);
      throw error;
    }
  }

  /**
   * 获取语音交互统计
   * @param userId 用户ID（可选）
   * @returns 统计信息
   */
  async getVoiceInteractionStats(userId?: string): Promise<any> {
    try {
      const queryBuilder = this.voiceCommandRepository.createQueryBuilder('vc');

      if (userId) {
        queryBuilder.where('vc.userId = :userId', { userId });
      }

      const totalCommands = await queryBuilder.getCount();
      
      const successRate = await queryBuilder
        .select('AVG(CASE WHEN vc.success = 1 THEN 1.0 ELSE 0.0 END)', 'successRate')
        .getRawOne();

      const avgConfidence = await queryBuilder
        .select('AVG(vc.recognitionConfidence)', 'avgConfidence')
        .getRawOne();

      const avgProcessingTime = await queryBuilder
        .select('AVG(vc.processingTime)', 'avgProcessingTime')
        .getRawOne();

      const commandTypes = await queryBuilder
        .select('vc.commandType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('vc.commandType')
        .getRawMany();

      return {
        totalCommands,
        successRate: parseFloat(successRate?.successRate || '0'),
        averageConfidence: parseFloat(avgConfidence?.avgConfidence || '0'),
        averageProcessingTime: parseFloat(avgProcessingTime?.avgProcessingTime || '0'),
        commandTypeDistribution: commandTypes,
      };

    } catch (error) {
      this.logger.error('获取语音交互统计失败:', error);
      throw error;
    }
  }

  /**
   * 配置语音参数
   * @param userId 用户ID
   * @param settings 语音设置
   */
  async configureVoiceSettings(userId: string, settings: any): Promise<void> {
    try {
      // 这里可以保存用户的语音偏好设置
      // 例如：语音、语速、音调等
      this.logger.log(`用户 ${userId} 配置语音设置:`, settings);
      
      // 实际实现中可以保存到数据库或缓存
    } catch (error) {
      this.logger.error('配置语音设置失败:', error);
      throw error;
    }
  }

  /**
   * 实时语音对话
   */
  async startVoiceConversation(userId: string, sessionId: string): Promise<any> {
    try {
      this.logger.log(`启动语音对话: 用户 ${userId}, 会话 ${sessionId}`);

      const conversation = {
        userId,
        sessionId,
        startTime: new Date(),
        isActive: true,
        messages: [],
        context: {},
        settings: {
          language: 'zh-CN',
          voice: 'zh-CN-XiaoxiaoNeural',
          autoResponse: true,
          contextAware: true
        }
      };

      // 这里可以保存到缓存或数据库
      // await this.conversationCache.set(sessionId, conversation);

      return {
        success: true,
        sessionId,
        message: '语音对话已启动',
        settings: conversation.settings
      };

    } catch (error) {
      this.logger.error('启动语音对话失败:', error);
      throw error;
    }
  }

  /**
   * 处理对话消息
   */
  async processConversationMessage(
    sessionId: string,
    audioData: Buffer | string,
    messageType: 'voice' | 'text' = 'voice'
  ): Promise<any> {
    try {
      let inputText = '';
      let confidence = 1.0;

      if (messageType === 'voice') {
        const recognitionResult = await this.speechRecognitionService.recognize(audioData);
        if (!recognitionResult.success) {
          return {
            success: false,
            error: '语音识别失败',
            details: recognitionResult.error
          };
        }
        inputText = recognitionResult.text;
        confidence = recognitionResult.confidence;
      } else {
        inputText = audioData.toString();
      }

      // 处理对话逻辑
      const response = await this.generateConversationResponse(sessionId, inputText);

      // 生成语音响应
      const audioResponse = await this.textToSpeechService.synthesize(response.text, {
        language: 'zh-CN',
        voice: 'zh-CN-XiaoxiaoNeural'
      });

      return {
        success: true,
        userMessage: inputText,
        confidence,
        response: response.text,
        audioResponse: audioResponse?.audioData,
        context: response.context,
        suggestions: response.suggestions
      };

    } catch (error) {
      this.logger.error('处理对话消息失败:', error);
      throw error;
    }
  }

  /**
   * 生成对话响应
   */
  private async generateConversationResponse(sessionId: string, inputText: string): Promise<any> {
    try {
      // 简化的对话逻辑，实际应用中可以集成更复杂的NLP和对话管理
      const responses = {
        greeting: ['您好！我是您的智能助手，有什么可以帮助您的吗？'],
        help: ['我可以帮助您进行设备操作、故障诊断、维护指导等。请告诉我您需要什么帮助。'],
        maintenance: ['请告诉我设备型号和具体问题，我会为您提供详细的维护指导。'],
        operation: ['我会为您提供操作步骤，请按照指示进行操作。'],
        default: ['我理解了您的问题，让我为您查找相关信息。']
      };

      let responseType = 'default';
      let responseText = '';

      // 简单的意图识别
      if (inputText.includes('你好') || inputText.includes('您好')) {
        responseType = 'greeting';
      } else if (inputText.includes('帮助') || inputText.includes('帮忙')) {
        responseType = 'help';
      } else if (inputText.includes('维护') || inputText.includes('保养')) {
        responseType = 'maintenance';
      } else if (inputText.includes('操作') || inputText.includes('使用')) {
        responseType = 'operation';
      }

      responseText = responses[responseType][0];

      return {
        text: responseText,
        type: responseType,
        context: { lastIntent: responseType },
        suggestions: this.generateSuggestions(responseType)
      };

    } catch (error) {
      this.logger.error('生成对话响应失败:', error);
      return {
        text: '抱歉，我暂时无法理解您的问题，请重新表述。',
        type: 'error',
        context: {},
        suggestions: []
      };
    }
  }

  /**
   * 生成建议
   */
  private generateSuggestions(responseType: string): string[] {
    const suggestions = {
      greeting: ['查看设备状态', '开始维护指导', '故障诊断'],
      help: ['设备操作指南', '维护计划', '故障排除'],
      maintenance: ['日常保养', '定期检查', '零件更换'],
      operation: ['开机流程', '参数设置', '安全注意事项'],
      default: ['重新描述问题', '查看帮助文档', '联系技术支持']
    };

    return suggestions[responseType] || suggestions.default;
  }

  /**
   * 语音情感分析
   */
  async analyzeVoiceEmotion(audioData: Buffer | string): Promise<any> {
    try {
      // 简化的情感分析，实际应用中需要专业的情感识别模型
      const analysis = {
        emotion: 'neutral',
        confidence: 0.8,
        valence: 0.0, // 情感效价 (-1到1)
        arousal: 0.0, // 情感唤醒度 (-1到1)
        stress: 0.0,  // 压力水平 (0到1)
        fatigue: 0.0  // 疲劳度 (0到1)
      };

      // 这里应该调用专业的语音情感分析API
      // 例如：Microsoft Cognitive Services, Google Cloud Speech-to-Text等

      return {
        success: true,
        analysis,
        recommendations: this.generateEmotionRecommendations(analysis)
      };

    } catch (error) {
      this.logger.error('语音情感分析失败:', error);
      throw error;
    }
  }

  /**
   * 生成情感建议
   */
  private generateEmotionRecommendations(analysis: any): string[] {
    const recommendations = [];

    if (analysis.stress > 0.7) {
      recommendations.push('检测到您可能有些紧张，建议先休息一下');
    }

    if (analysis.fatigue > 0.7) {
      recommendations.push('检测到您可能有些疲劳，建议适当休息');
    }

    if (analysis.valence < -0.5) {
      recommendations.push('我会尽力为您提供帮助，请保持耐心');
    }

    if (recommendations.length === 0) {
      recommendations.push('您的状态很好，我们可以继续进行操作');
    }

    return recommendations;
  }

  /**
   * 语音指令学习
   */
  async learnCustomVoiceCommand(
    userId: string,
    commandText: string,
    action: string,
    parameters?: any
  ): Promise<any> {
    try {
      this.logger.log(`学习自定义语音指令: ${commandText} -> ${action}`);

      // 创建自定义指令
      const customCommand = {
        userId,
        commandText: commandText.toLowerCase(),
        action,
        parameters: parameters || {},
        createdAt: new Date(),
        isActive: true,
        usageCount: 0
      };

      // 保存到数据库
      // await this.customCommandRepository.save(customCommand);

      // 训练语音识别模型（如果支持）
      await this.trainVoiceRecognition(userId, commandText);

      return {
        success: true,
        message: '自定义语音指令已学习',
        command: customCommand
      };

    } catch (error) {
      this.logger.error('学习自定义语音指令失败:', error);
      throw error;
    }
  }

  /**
   * 训练语音识别
   */
  private async trainVoiceRecognition(userId: string, commandText: string): Promise<void> {
    try {
      // 这里可以实现用户特定的语音识别训练
      // 例如：适应用户的口音、语速等特征
      this.logger.log(`为用户 ${userId} 训练语音识别: ${commandText}`);

      // 实际实现中可能需要：
      // 1. 收集用户的语音样本
      // 2. 提取声学特征
      // 3. 更新用户特定的语音模型
      // 4. 优化识别准确率

    } catch (error) {
      this.logger.error('训练语音识别失败:', error);
    }
  }

  /**
   * 获取语音质量评估
   */
  async assessVoiceQuality(audioData: Buffer | string): Promise<any> {
    try {
      const assessment = {
        overallQuality: 0.8, // 总体质量 (0-1)
        clarity: 0.85,       // 清晰度
        volume: 0.75,        // 音量适中度
        noiseLevel: 0.2,     // 噪音水平 (0-1, 越低越好)
        signalToNoise: 15.5, // 信噪比 (dB)
        recommendations: []
      };

      // 生成改进建议
      if (assessment.clarity < 0.7) {
        assessment.recommendations.push('请尽量清晰地发音');
      }
      if (assessment.volume < 0.5) {
        assessment.recommendations.push('请提高音量');
      } else if (assessment.volume > 0.9) {
        assessment.recommendations.push('请降低音量');
      }
      if (assessment.noiseLevel > 0.5) {
        assessment.recommendations.push('请减少环境噪音');
      }

      return {
        success: true,
        assessment
      };

    } catch (error) {
      this.logger.error('语音质量评估失败:', error);
      throw error;
    }
  }

  /**
   * 保存语音指令记录
   */
  private async saveVoiceCommand(data: any): Promise<VoiceCommand> {
    try {
      const voiceCommand = this.voiceCommandRepository.create(data);
      const saved = await this.voiceCommandRepository.save(voiceCommand);
      return Array.isArray(saved) ? saved[0] : saved;
    } catch (error) {
      this.logger.error('保存语音指令记录失败:', error);
      throw error;
    }
  }
}
