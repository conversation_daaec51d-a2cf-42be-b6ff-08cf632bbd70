import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HandTrackingService } from './hand-tracking.service';
import { GesturePatternService } from './gesture-pattern.service';
import { MotionAnalysisService } from './motion-analysis.service';

/**
 * 手势识别服务
 * 统一管理手势识别、动作捕捉和模式匹配
 */
@Injectable()
export class GestureRecognitionService {
  private readonly logger = new Logger(GestureRecognitionService.name);
  private isInitialized = false;
  private recognitionSession: Map<string, any> = new Map();

  constructor(
    private readonly handTrackingService: HandTrackingService,
    private readonly gesturePatternService: GesturePatternService,
    private readonly motionAnalysisService: MotionAnalysisService,
    private readonly configService: ConfigService,
  ) {
    this.initializeService();
  }

  /**
   * 初始化手势识别服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 初始化手部追踪
      await this.handTrackingService.initialize();
      
      // 加载手势模式
      await this.gesturePatternService.loadPatterns();
      
      // 初始化运动分析
      await this.motionAnalysisService.initialize();
      
      this.isInitialized = true;
      this.logger.log('手势识别服务初始化完成');
    } catch (error) {
      this.logger.error('初始化手势识别服务失败:', error);
    }
  }

  /**
   * 开始手势识别会话
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @param options 识别选项
   * @returns 会话信息
   */
  async startRecognitionSession(
    userId: string,
    sessionId: string,
    options?: any,
  ): Promise<any> {
    try {
      if (!this.isInitialized) {
        throw new Error('手势识别服务未初始化');
      }

      const session = {
        userId,
        sessionId,
        startTime: new Date(),
        isActive: true,
        recognizedGestures: [],
        currentFrame: 0,
        options: {
          confidenceThreshold: options?.confidenceThreshold || 0.8,
          trackingMode: options?.trackingMode || 'hands',
          enableMotionAnalysis: options?.enableMotionAnalysis !== false,
          bufferSize: options?.bufferSize || 10,
          ...options,
        },
      };

      this.recognitionSession.set(sessionId, session);

      this.logger.log(`手势识别会话已启动: ${sessionId} - 用户: ${userId}`);
      return {
        success: true,
        sessionId,
        message: '手势识别会话已启动',
        options: session.options,
      };

    } catch (error) {
      this.logger.error('启动手势识别会话失败:', error);
      throw error;
    }
  }

  /**
   * 处理视频帧
   * @param sessionId 会话ID
   * @param frameData 帧数据
   * @returns 识别结果
   */
  async processFrame(sessionId: string, frameData: any): Promise<any> {
    try {
      const session = this.recognitionSession.get(sessionId);
      if (!session || !session.isActive) {
        throw new Error(`会话不存在或已结束: ${sessionId}`);
      }

      const startTime = Date.now();

      // 1. 手部追踪
      const handTrackingResult = await this.handTrackingService.trackHands(frameData);

      if (!handTrackingResult.success || handTrackingResult.hands.length === 0) {
        return {
          success: false,
          message: '未检测到手部',
          frameNumber: session.currentFrame++,
          processingTime: Date.now() - startTime,
        };
      }

      // 2. 手势识别
      const gestureResults = [];
      for (const hand of handTrackingResult.hands) {
        const gestureResult = await this.recognizeGesture(hand, session.options);
        if (gestureResult.success) {
          gestureResults.push(gestureResult);
        }
      }

      // 3. 运动分析（如果启用）
      let motionAnalysis = null;
      if (session.options.enableMotionAnalysis) {
        motionAnalysis = await this.motionAnalysisService.analyzeMotion(
          handTrackingResult.hands,
          session.sessionId,
        );
      }

      // 4. 更新会话状态
      session.currentFrame++;
      if (gestureResults.length > 0) {
        session.recognizedGestures.push(...gestureResults);
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        frameNumber: session.currentFrame - 1,
        hands: handTrackingResult.hands,
        gestures: gestureResults,
        motionAnalysis,
        processingTime,
        sessionStats: {
          totalFrames: session.currentFrame,
          totalGestures: session.recognizedGestures.length,
          sessionDuration: Date.now() - session.startTime.getTime(),
        },
      };

    } catch (error) {
      this.logger.error('处理视频帧失败:', error);
      throw error;
    }
  }

  /**
   * 识别手势
   * @param handData 手部数据
   * @param options 识别选项
   * @returns 识别结果
   */
  private async recognizeGesture(handData: any, options: any): Promise<any> {
    try {
      // 获取手势模式
      const patterns = await this.gesturePatternService.getActivePatterns();

      let bestMatch = null;
      let maxConfidence = 0;

      // 遍历所有模式进行匹配
      for (const pattern of patterns) {
        const confidence = await this.calculateGestureConfidence(handData, pattern);
        
        if (confidence > maxConfidence && confidence >= options.confidenceThreshold) {
          maxConfidence = confidence;
          bestMatch = pattern;
        }
      }

      if (bestMatch) {
        // 更新模式使用统计
        await this.gesturePatternService.updateUsageStats(bestMatch.id, maxConfidence);

        return {
          success: true,
          gesture: {
            id: bestMatch.id,
            name: bestMatch.patternName,
            type: bestMatch.gestureType,
            confidence: maxConfidence,
            action: bestMatch.associatedAction,
            parameters: bestMatch.actionParameters,
          },
          handData,
          timestamp: new Date(),
        };
      }

      return {
        success: false,
        message: '未识别到已知手势',
        confidence: maxConfidence,
      };

    } catch (error) {
      this.logger.error('识别手势失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 计算手势置信度
   * @param handData 手部数据
   * @param pattern 手势模式
   * @returns 置信度
   */
  private async calculateGestureConfidence(handData: any, pattern: any): Promise<number> {
    try {
      let confidence = 0;

      switch (pattern.gestureType) {
        case 'static':
          confidence = this.calculateStaticGestureConfidence(handData, pattern);
          break;
        case 'dynamic':
          confidence = this.calculateDynamicGestureConfidence(handData, pattern);
          break;
        case 'sequence':
          confidence = this.calculateSequenceGestureConfidence(handData, pattern);
          break;
        default:
          confidence = 0;
      }

      return Math.max(0, Math.min(1, confidence));

    } catch (error) {
      this.logger.error('计算手势置信度失败:', error);
      return 0;
    }
  }

  /**
   * 计算静态手势置信度
   */
  private calculateStaticGestureConfidence(handData: any, pattern: any): number {
    // 简化的静态手势匹配算法
    const landmarks = handData.landmarks;
    const patternKeyPoints = pattern.keyPoints;

    if (!landmarks || !patternKeyPoints) return 0;

    let totalDistance = 0;
    let validPoints = 0;

    for (let i = 0; i < Math.min(landmarks.length, patternKeyPoints.length); i++) {
      const landmark = landmarks[i];
      const keyPoint = patternKeyPoints[i];

      if (landmark && keyPoint) {
        const distance = Math.sqrt(
          Math.pow(landmark.x - keyPoint.x, 2) +
          Math.pow(landmark.y - keyPoint.y, 2) +
          Math.pow(landmark.z - keyPoint.z, 2)
        );
        totalDistance += distance;
        validPoints++;
      }
    }

    if (validPoints === 0) return 0;

    const averageDistance = totalDistance / validPoints;
    const maxDistance = 0.1; // 最大允许距离
    
    return Math.max(0, 1 - averageDistance / maxDistance);
  }

  /**
   * 计算动态手势置信度
   */
  private calculateDynamicGestureConfidence(handData: any, pattern: any): number {
    // 简化的动态手势匹配算法
    // 实际实现中需要考虑时间序列和运动轨迹
    return 0.7; // 模拟置信度
  }

  /**
   * 计算序列手势置信度
   */
  private calculateSequenceGestureConfidence(handData: any, pattern: any): number {
    // 简化的序列手势匹配算法
    // 实际实现中需要维护手势序列状态
    return 0.6; // 模拟置信度
  }

  /**
   * 停止手势识别会话
   * @param sessionId 会话ID
   * @returns 会话报告
   */
  async stopRecognitionSession(sessionId: string): Promise<any> {
    try {
      const session = this.recognitionSession.get(sessionId);
      if (!session) {
        throw new Error(`会话不存在: ${sessionId}`);
      }

      session.isActive = false;
      session.endTime = new Date();

      // 生成会话报告
      const report = {
        sessionId,
        userId: session.userId,
        duration: session.endTime.getTime() - session.startTime.getTime(),
        totalFrames: session.currentFrame,
        totalGestures: session.recognizedGestures.length,
        recognizedGestures: session.recognizedGestures,
        averageProcessingTime: this.calculateAverageProcessingTime(session),
        gestureFrequency: this.calculateGestureFrequency(session),
      };

      // 清理会话数据
      this.recognitionSession.delete(sessionId);

      this.logger.log(`手势识别会话已结束: ${sessionId}`);
      return report;

    } catch (error) {
      this.logger.error('停止手势识别会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取支持的手势列表
   */
  async getSupportedGestures(): Promise<any[]> {
    try {
      return await this.gesturePatternService.getAllPatterns();
    } catch (error) {
      this.logger.error('获取支持手势列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取手势识别统计
   */
  async getRecognitionStats(): Promise<any> {
    try {
      const activeSessions = Array.from(this.recognitionSession.values()).filter(s => s.isActive);
      const patternStats = await this.gesturePatternService.getUsageStatistics();

      return {
        activeSessions: activeSessions.length,
        totalPatterns: patternStats.totalPatterns,
        mostUsedGestures: patternStats.mostUsed,
        averageAccuracy: patternStats.averageAccuracy,
        systemPerformance: {
          memoryUsage: process.memoryUsage(),
          uptime: process.uptime(),
        },
      };

    } catch (error) {
      this.logger.error('获取手势识别统计失败:', error);
      throw error;
    }
  }

  /**
   * 计算平均处理时间
   */
  private calculateAverageProcessingTime(session: any): number {
    // 简化实现，实际中需要记录每帧的处理时间
    return 50; // 模拟50ms平均处理时间
  }

  /**
   * 计算手势频率
   */
  private calculateGestureFrequency(session: any): any {
    const frequency: { [key: string]: number } = {};

    session.recognizedGestures.forEach((gesture: any) => {
      const name = gesture.gesture.name;
      frequency[name] = (frequency[name] || 0) + 1;
    });

    return frequency;
  }

  /**
   * 训练自定义手势
   */
  async trainCustomGesture(
    userId: string,
    gestureName: string,
    trainingData: any[]
  ): Promise<any> {
    try {
      this.logger.log(`开始训练自定义手势: ${gestureName} - 用户: ${userId}`);

      // 验证训练数据
      if (!trainingData || trainingData.length < 5) {
        throw new Error('训练数据不足，至少需要5个样本');
      }

      // 提取特征
      const features = trainingData.map(sample => this.extractGestureFeatures(sample));

      // 计算平均特征向量
      const averageFeatures = this.calculateAverageFeatures(features);

      // 计算标准差用于置信度计算
      const standardDeviation = this.calculateStandardDeviation(features, averageFeatures);

      // 创建手势模式
      const gesturePattern = {
        id: `custom_${Date.now()}_${userId}`,
        patternName: gestureName,
        gestureType: 'custom',
        userId,
        keyPoints: averageFeatures,
        standardDeviation,
        trainingDataCount: trainingData.length,
        createdAt: new Date(),
        isActive: true,
        associatedAction: `custom_action_${gestureName}`,
        actionParameters: {
          customGesture: true,
          gestureName
        }
      };

      // 保存手势模式
      await this.gesturePatternService.savePattern(gesturePattern);

      this.logger.log(`自定义手势训练完成: ${gestureName}`);

      return {
        success: true,
        gestureId: gesturePattern.id,
        gestureName,
        trainingDataCount: trainingData.length,
        message: '自定义手势训练完成'
      };

    } catch (error) {
      this.logger.error('训练自定义手势失败:', error);
      throw error;
    }
  }

  /**
   * 提取手势特征
   */
  private extractGestureFeatures(gestureData: any): any {
    if (!gestureData.landmarks) {
      throw new Error('手势数据缺少关键点信息');
    }

    const features = {
      landmarks: gestureData.landmarks,
      fingerAngles: this.calculateFingerAngles(gestureData.landmarks),
      palmOrientation: this.calculatePalmOrientation(gestureData.landmarks),
      handShape: this.calculateHandShape(gestureData.landmarks),
      relativePositions: this.calculateRelativePositions(gestureData.landmarks)
    };

    return features;
  }

  /**
   * 计算手指角度
   */
  private calculateFingerAngles(landmarks: any[]): number[] {
    const angles = [];

    // 拇指角度
    if (landmarks[4] && landmarks[3] && landmarks[2]) {
      const thumbAngle = this.calculateAngle(landmarks[2], landmarks[3], landmarks[4]);
      angles.push(thumbAngle);
    }

    // 食指角度
    if (landmarks[8] && landmarks[7] && landmarks[6]) {
      const indexAngle = this.calculateAngle(landmarks[6], landmarks[7], landmarks[8]);
      angles.push(indexAngle);
    }

    // 中指角度
    if (landmarks[12] && landmarks[11] && landmarks[10]) {
      const middleAngle = this.calculateAngle(landmarks[10], landmarks[11], landmarks[12]);
      angles.push(middleAngle);
    }

    // 无名指角度
    if (landmarks[16] && landmarks[15] && landmarks[14]) {
      const ringAngle = this.calculateAngle(landmarks[14], landmarks[15], landmarks[16]);
      angles.push(ringAngle);
    }

    // 小指角度
    if (landmarks[20] && landmarks[19] && landmarks[18]) {
      const pinkyAngle = this.calculateAngle(landmarks[18], landmarks[19], landmarks[20]);
      angles.push(pinkyAngle);
    }

    return angles;
  }

  /**
   * 计算角度
   */
  private calculateAngle(p1: any, p2: any, p3: any): number {
    const v1 = { x: p1.x - p2.x, y: p1.y - p2.y, z: p1.z - p2.z };
    const v2 = { x: p3.x - p2.x, y: p3.y - p2.y, z: p3.z - p2.z };

    const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);

    if (mag1 === 0 || mag2 === 0) return 0;

    const cosAngle = dot / (mag1 * mag2);
    return Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  }

  /**
   * 计算手掌方向
   */
  private calculatePalmOrientation(landmarks: any[]): any {
    if (!landmarks[0] || !landmarks[5] || !landmarks[17]) {
      return { x: 0, y: 0, z: 0 };
    }

    // 使用手腕、食指根部和小指根部计算手掌法向量
    const wrist = landmarks[0];
    const indexBase = landmarks[5];
    const pinkyBase = landmarks[17];

    const v1 = {
      x: indexBase.x - wrist.x,
      y: indexBase.y - wrist.y,
      z: indexBase.z - wrist.z
    };

    const v2 = {
      x: pinkyBase.x - wrist.x,
      y: pinkyBase.y - wrist.y,
      z: pinkyBase.z - wrist.z
    };

    // 计算叉积得到法向量
    const normal = {
      x: v1.y * v2.z - v1.z * v2.y,
      y: v1.z * v2.x - v1.x * v2.z,
      z: v1.x * v2.y - v1.y * v2.x
    };

    // 归一化
    const magnitude = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
    if (magnitude === 0) return { x: 0, y: 0, z: 0 };

    return {
      x: normal.x / magnitude,
      y: normal.y / magnitude,
      z: normal.z / magnitude
    };
  }

  /**
   * 计算手型
   */
  private calculateHandShape(landmarks: any[]): any {
    const shape = {
      openness: 0, // 手的张开程度
      curvature: 0, // 手指弯曲程度
      spread: 0 // 手指分散程度
    };

    if (landmarks.length < 21) return shape;

    // 计算手指张开程度
    const fingerTips = [landmarks[4], landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    const palm = landmarks[0];

    let totalDistance = 0;
    fingerTips.forEach(tip => {
      if (tip && palm) {
        const distance = Math.sqrt(
          Math.pow(tip.x - palm.x, 2) +
          Math.pow(tip.y - palm.y, 2) +
          Math.pow(tip.z - palm.z, 2)
        );
        totalDistance += distance;
      }
    });

    shape.openness = totalDistance / fingerTips.length;

    // 计算手指弯曲程度（简化）
    const fingerAngles = this.calculateFingerAngles(landmarks);
    shape.curvature = fingerAngles.reduce((sum, angle) => sum + angle, 0) / fingerAngles.length;

    // 计算手指分散程度
    if (fingerTips.length >= 2) {
      let spreadSum = 0;
      for (let i = 0; i < fingerTips.length - 1; i++) {
        const tip1 = fingerTips[i];
        const tip2 = fingerTips[i + 1];
        if (tip1 && tip2) {
          const distance = Math.sqrt(
            Math.pow(tip1.x - tip2.x, 2) +
            Math.pow(tip1.y - tip2.y, 2) +
            Math.pow(tip1.z - tip2.z, 2)
          );
          spreadSum += distance;
        }
      }
      shape.spread = spreadSum / (fingerTips.length - 1);
    }

    return shape;
  }

  /**
   * 计算相对位置
   */
  private calculateRelativePositions(landmarks: any[]): any[] {
    if (!landmarks[0]) return [];

    const wrist = landmarks[0];
    return landmarks.map(landmark => {
      if (!landmark) return { x: 0, y: 0, z: 0 };

      return {
        x: landmark.x - wrist.x,
        y: landmark.y - wrist.y,
        z: landmark.z - wrist.z
      };
    });
  }

  /**
   * 计算平均特征
   */
  private calculateAverageFeatures(features: any[]): any {
    if (features.length === 0) return null;

    const average = {
      landmarks: [],
      fingerAngles: [],
      palmOrientation: { x: 0, y: 0, z: 0 },
      handShape: { openness: 0, curvature: 0, spread: 0 },
      relativePositions: []
    };

    // 计算关键点平均值
    const landmarkCount = features[0].landmarks.length;
    for (let i = 0; i < landmarkCount; i++) {
      let sumX = 0, sumY = 0, sumZ = 0;
      features.forEach(feature => {
        if (feature.landmarks[i]) {
          sumX += feature.landmarks[i].x;
          sumY += feature.landmarks[i].y;
          sumZ += feature.landmarks[i].z;
        }
      });
      average.landmarks.push({
        x: sumX / features.length,
        y: sumY / features.length,
        z: sumZ / features.length
      });
    }

    // 计算手指角度平均值
    const angleCount = features[0].fingerAngles.length;
    for (let i = 0; i < angleCount; i++) {
      let sum = 0;
      features.forEach(feature => {
        sum += feature.fingerAngles[i] || 0;
      });
      average.fingerAngles.push(sum / features.length);
    }

    // 计算手掌方向平均值
    features.forEach(feature => {
      average.palmOrientation.x += feature.palmOrientation.x;
      average.palmOrientation.y += feature.palmOrientation.y;
      average.palmOrientation.z += feature.palmOrientation.z;
    });
    average.palmOrientation.x /= features.length;
    average.palmOrientation.y /= features.length;
    average.palmOrientation.z /= features.length;

    // 计算手型平均值
    features.forEach(feature => {
      average.handShape.openness += feature.handShape.openness;
      average.handShape.curvature += feature.handShape.curvature;
      average.handShape.spread += feature.handShape.spread;
    });
    average.handShape.openness /= features.length;
    average.handShape.curvature /= features.length;
    average.handShape.spread /= features.length;

    return average;
  }

  /**
   * 计算标准差
   */
  private calculateStandardDeviation(features: any[], average: any): any {
    if (features.length <= 1) return null;

    const variance = {
      landmarks: [],
      fingerAngles: [],
      palmOrientation: { x: 0, y: 0, z: 0 },
      handShape: { openness: 0, curvature: 0, spread: 0 }
    };

    // 计算关键点方差
    for (let i = 0; i < average.landmarks.length; i++) {
      let sumSqDiffX = 0, sumSqDiffY = 0, sumSqDiffZ = 0;
      features.forEach(feature => {
        if (feature.landmarks[i]) {
          sumSqDiffX += Math.pow(feature.landmarks[i].x - average.landmarks[i].x, 2);
          sumSqDiffY += Math.pow(feature.landmarks[i].y - average.landmarks[i].y, 2);
          sumSqDiffZ += Math.pow(feature.landmarks[i].z - average.landmarks[i].z, 2);
        }
      });
      variance.landmarks.push({
        x: Math.sqrt(sumSqDiffX / features.length),
        y: Math.sqrt(sumSqDiffY / features.length),
        z: Math.sqrt(sumSqDiffZ / features.length)
      });
    }

    return variance;
  }
}
