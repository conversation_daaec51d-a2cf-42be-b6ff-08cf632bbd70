import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as d3 from 'd3';
import { Chart, ChartConfiguration } from 'chart.js';
import moment from 'moment';
import * as ss from 'simple-statistics';
import { ProductionDataEntity } from '../entities/production-data.entity';

/**
 * 图表配置接口
 */
interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap' | 'gauge' | 'treemap';
  title: string;
  data: any;
  options?: any;
  responsive?: boolean;
  interactive?: boolean;
}

/**
 * 仪表板配置接口
 */
interface DashboardConfig {
  id: string;
  title: string;
  layout: 'grid' | 'flex' | 'masonry';
  widgets: WidgetConfig[];
  refreshInterval?: number;
  filters?: any;
}

/**
 * 组件配置接口
 */
interface WidgetConfig {
  id: string;
  type: 'chart' | 'kpi' | 'table' | 'alert' | 'text';
  title: string;
  position: { x: number; y: number; width: number; height: number };
  config: any;
  dataSource?: string;
}

/**
 * 可视化主题接口
 */
interface VisualizationTheme {
  name: string;
  colors: {
    primary: string[];
    secondary: string[];
    background: string;
    text: string;
    grid: string;
  };
  fonts: {
    family: string;
    sizes: { [key: string]: number };
  };
}

/**
 * 可视化服务
 */
@Injectable()
export class VisualizationService {
  private readonly logger = new Logger(VisualizationService.name);

  // 预定义主题
  private readonly themes: Map<string, VisualizationTheme> = new Map();

  // 图表模板
  private readonly chartTemplates: Map<string, ChartConfig> = new Map();

  // 仪表板缓存
  private readonly dashboardCache: Map<string, any> = new Map();

  constructor(
    @InjectRepository(ProductionDataEntity)
    private productionDataRepository: Repository<ProductionDataEntity>
  ) {
    this.initializeThemes();
    this.initializeChartTemplates();
  }

  /**
   * 创建生产效率图表
   */
  async createProductionEfficiencyChart(
    startDate: Date,
    endDate: Date,
    deviceIds?: string[],
    chartType: 'line' | 'bar' = 'line'
  ): Promise<ChartConfig> {
    try {
      const data = await this.getProductionData(startDate, endDate, deviceIds);

      const chartData = {
        labels: data.map(d => moment(d.timestamp).format('MM-DD HH:mm')),
        datasets: [{
          label: '生产效率 (%)',
          data: data.map(d => d.efficiency),
          borderColor: '#4CAF50',
          backgroundColor: chartType === 'bar' ? '#4CAF50' : 'rgba(76, 175, 80, 0.1)',
          fill: chartType === 'line',
          tension: 0.4
        }]
      };

      return {
        type: chartType,
        title: '生产效率趋势',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: '生产效率趋势分析'
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '效率 (%)'
              }
            },
            x: {
              title: {
                display: true,
                text: '时间'
              }
            }
          }
        },
        responsive: true,
        interactive: true
      };

    } catch (error) {
      this.logger.error('创建生产效率图表失败', error);
      throw error;
    }
  }

  /**
   * 创建质量分析图表
   */
  async createQualityAnalysisChart(
    startDate: Date,
    endDate: Date,
    deviceIds?: string[]
  ): Promise<ChartConfig> {
    try {
      const data = await this.getProductionData(startDate, endDate, deviceIds);

      // 质量分布分析
      const qualityRanges = [
        { range: '95-100%', count: 0, color: '#4CAF50' },
        { range: '90-95%', count: 0, color: '#FFC107' },
        { range: '85-90%', count: 0, color: '#FF9800' },
        { range: '<85%', count: 0, color: '#F44336' }
      ];

      data.forEach(d => {
        if (d.quality >= 95) qualityRanges[0].count++;
        else if (d.quality >= 90) qualityRanges[1].count++;
        else if (d.quality >= 85) qualityRanges[2].count++;
        else qualityRanges[3].count++;
      });

      const chartData = {
        labels: qualityRanges.map(r => r.range),
        datasets: [{
          label: '质量分布',
          data: qualityRanges.map(r => r.count),
          backgroundColor: qualityRanges.map(r => r.color),
          borderWidth: 1
        }]
      };

      return {
        type: 'pie',
        title: '质量分布分析',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: '产品质量分布'
            },
            legend: {
              display: true,
              position: 'right'
            }
          }
        },
        responsive: true,
        interactive: true
      };

    } catch (error) {
      this.logger.error('创建质量分析图表失败', error);
      throw error;
    }
  }

  /**
   * 创建能耗分析图表
   */
  async createEnergyConsumptionChart(
    startDate: Date,
    endDate: Date,
    deviceIds?: string[]
  ): Promise<ChartConfig> {
    try {
      const data = await this.getProductionData(startDate, endDate, deviceIds);

      // 按设备分组
      const deviceGroups = new Map<string, any[]>();
      data.forEach(d => {
        if (!deviceGroups.has(d.deviceId)) {
          deviceGroups.set(d.deviceId, []);
        }
        deviceGroups.get(d.deviceId)!.push(d);
      });

      const datasets = Array.from(deviceGroups.entries()).map(([deviceId, deviceData], index) => {
        const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'];
        return {
          label: deviceId,
          data: deviceData.map(d => ({
            x: moment(d.timestamp).format('MM-DD HH:mm'),
            y: d.energyConsumption
          })),
          borderColor: colors[index % colors.length],
          backgroundColor: colors[index % colors.length] + '20',
          fill: false,
          tension: 0.4
        };
      });

      return {
        type: 'line',
        title: '设备能耗分析',
        data: {
          datasets
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: '设备能耗趋势对比'
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '能耗 (kWh)'
              }
            },
            x: {
              type: 'category',
              title: {
                display: true,
                text: '时间'
              }
            }
          }
        },
        responsive: true,
        interactive: true
      };

    } catch (error) {
      this.logger.error('创建能耗分析图表失败', error);
      throw error;
    }
  }

  /**
   * 创建OEE仪表盘
   */
  async createOEEGaugeChart(
    startDate: Date,
    endDate: Date,
    deviceIds?: string[]
  ): Promise<ChartConfig> {
    try {
      const data = await this.getProductionData(startDate, endDate, deviceIds);

      if (data.length === 0) {
        throw new Error('没有可用数据');
      }

      // 计算OEE
      const avgAvailability = this.calculateAvailability(data);
      const avgPerformance = ss.mean(data.map(d => d.efficiency));
      const avgQuality = ss.mean(data.map(d => d.quality));
      const oee = (avgAvailability * avgPerformance * avgQuality) / 10000;

      // 仪表盘数据
      const gaugeData = {
        datasets: [{
          data: [oee, 100 - oee],
          backgroundColor: [
            oee >= 85 ? '#4CAF50' : oee >= 70 ? '#FFC107' : '#F44336',
            '#E0E0E0'
          ],
          borderWidth: 0,
          cutout: '70%'
        }]
      };

      return {
        type: 'pie',
        title: 'OEE 仪表盘',
        data: gaugeData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: `OEE: ${oee.toFixed(1)}%`
            },
            legend: {
              display: false
            },
            tooltip: {
              enabled: false
            }
          },
          elements: {
            center: {
              text: `${oee.toFixed(1)}%`,
              color: '#333',
              fontStyle: 'Arial',
              sidePadding: 20,
              minFontSize: 25,
              lineHeight: 25
            }
          }
        },
        responsive: true,
        interactive: true
      };

    } catch (error) {
      this.logger.error('创建OEE仪表盘失败', error);
      throw error;
    }
  }

  /**
   * 创建热力图
   */
  async createHeatmapChart(
    startDate: Date,
    endDate: Date,
    metric: string = 'efficiency'
  ): Promise<ChartConfig> {
    try {
      const data = await this.getProductionData(startDate, endDate);

      // 按小时和设备分组
      const heatmapData: any[] = [];
      const deviceIds = [...new Set(data.map(d => d.deviceId))];
      const hours = Array.from({ length: 24 }, (_, i) => i);

      deviceIds.forEach((deviceId, deviceIndex) => {
        hours.forEach(hour => {
          const hourData = data.filter(d =>
            d.deviceId === deviceId &&
            moment(d.timestamp).hour() === hour
          );

          const avgValue = hourData.length > 0 ?
            ss.mean(hourData.map(d => (d as any)[metric])) : 0;

          heatmapData.push({
            x: hour,
            y: deviceIndex,
            v: avgValue
          });
        });
      });

      return {
        type: 'heatmap',
        title: `${metric} 热力图`,
        data: {
          datasets: [{
            label: metric,
            data: heatmapData,
            backgroundColor: (ctx: any) => {
              const value = ctx.parsed.v;
              const alpha = value / 100;
              return `rgba(76, 175, 80, ${alpha})`;
            }
          }]
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: `${metric} 24小时热力图`
            },
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              type: 'linear',
              position: 'bottom',
              min: 0,
              max: 23,
              title: {
                display: true,
                text: '小时'
              }
            },
            y: {
              type: 'linear',
              min: 0,
              max: deviceIds.length - 1,
              title: {
                display: true,
                text: '设备'
              },
              ticks: {
                callback: function(value: any) {
                  return deviceIds[value] || '';
                }
              }
            }
          }
        },
        responsive: true,
        interactive: true
      };

    } catch (error) {
      this.logger.error('创建热力图失败', error);
      throw error;
    }
  }

  /**
   * 创建自定义仪表板
   */
  async createCustomDashboard(config: DashboardConfig): Promise<any> {
    try {
      const dashboard = {
        id: config.id,
        title: config.title,
        layout: config.layout,
        widgets: [],
        metadata: {
          createdAt: new Date(),
          refreshInterval: config.refreshInterval || 30000
        }
      };

      // 处理每个组件
      for (const widgetConfig of config.widgets) {
        const widget = await this.createWidget(widgetConfig);
        dashboard.widgets.push(widget);
      }

      // 缓存仪表板
      this.dashboardCache.set(config.id, dashboard);

      this.logger.log(`自定义仪表板创建完成: ${config.title}`);
      return dashboard;

    } catch (error) {
      this.logger.error('创建自定义仪表板失败', error);
      throw error;
    }
  }

  /**
   * 导出图表为图片
   */
  async exportChartAsImage(
    chartConfig: ChartConfig,
    format: 'png' | 'jpeg' | 'svg' = 'png',
    width: number = 800,
    height: number = 600
  ): Promise<Buffer> {
    try {
      // 这里应该使用无头浏览器或Canvas库来渲染图表
      // 简化实现，返回模拟数据
      const mockImageBuffer = Buffer.from('mock-chart-image-data');

      this.logger.log(`图表导出为${format}格式: ${chartConfig.title}`);
      return mockImageBuffer;

    } catch (error) {
      this.logger.error('导出图表失败', error);
      throw error;
    }
  }

  /**
   * 获取可用主题列表
   */
  getAvailableThemes(): string[] {
    return Array.from(this.themes.keys());
  }

  /**
   * 应用主题到图表
   */
  applyThemeToChart(chartConfig: ChartConfig, themeName: string): ChartConfig {
    const theme = this.themes.get(themeName);
    if (!theme) {
      this.logger.warn(`主题不存在: ${themeName}`);
      return chartConfig;
    }

    // 应用主题颜色
    const themedConfig = { ...chartConfig };

    if (themedConfig.data.datasets) {
      themedConfig.data.datasets.forEach((dataset: any, index: number) => {
        dataset.borderColor = theme.colors.primary[index % theme.colors.primary.length];
        dataset.backgroundColor = theme.colors.primary[index % theme.colors.primary.length] + '20';
      });
    }

    // 应用主题选项
    if (!themedConfig.options) themedConfig.options = {};
    themedConfig.options.plugins = {
      ...themedConfig.options.plugins,
      legend: {
        ...themedConfig.options.plugins?.legend,
        labels: {
          color: theme.colors.text,
          font: {
            family: theme.fonts.family
          }
        }
      }
    };

    return themedConfig;
  }

  // 私有方法

  /**
   * 初始化主题
   */
  private initializeThemes(): void {
    // 默认主题
    this.themes.set('default', {
      name: '默认主题',
      colors: {
        primary: ['#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0'],
        secondary: ['#81C784', '#64B5F6', '#FFB74D', '#E57373', '#BA68C8'],
        background: '#FFFFFF',
        text: '#333333',
        grid: '#E0E0E0'
      },
      fonts: {
        family: 'Arial, sans-serif',
        sizes: { title: 16, label: 12, legend: 10 }
      }
    });

    // 深色主题
    this.themes.set('dark', {
      name: '深色主题',
      colors: {
        primary: ['#66BB6A', '#42A5F5', '#FFA726', '#EF5350', '#AB47BC'],
        secondary: ['#A5D6A7', '#90CAF9', '#FFCC02', '#FFAB91', '#CE93D8'],
        background: '#1E1E1E',
        text: '#FFFFFF',
        grid: '#404040'
      },
      fonts: {
        family: 'Arial, sans-serif',
        sizes: { title: 16, label: 12, legend: 10 }
      }
    });

    // 工业主题
    this.themes.set('industrial', {
      name: '工业主题',
      colors: {
        primary: ['#607D8B', '#795548', '#FF5722', '#009688', '#3F51B5'],
        secondary: ['#90A4AE', '#A1887F', '#FF8A65', '#4DB6AC', '#7986CB'],
        background: '#F5F5F5',
        text: '#263238',
        grid: '#BDBDBD'
      },
      fonts: {
        family: 'Roboto, sans-serif',
        sizes: { title: 18, label: 14, legend: 12 }
      }
    });

    this.logger.log('可视化主题初始化完成');
  }

  /**
   * 初始化图表模板
   */
  private initializeChartTemplates(): void {
    // KPI卡片模板
    this.chartTemplates.set('kpi-card', {
      type: 'gauge',
      title: 'KPI指标',
      data: {},
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });

    // 趋势线模板
    this.chartTemplates.set('trend-line', {
      type: 'line',
      title: '趋势分析',
      data: {},
      options: {
        responsive: true,
        elements: {
          point: {
            radius: 3
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });

    this.logger.log('图表模板初始化完成');
  }

  /**
   * 创建组件
   */
  private async createWidget(config: WidgetConfig): Promise<any> {
    switch (config.type) {
      case 'chart':
        return await this.createChartWidget(config);
      case 'kpi':
        return await this.createKPIWidget(config);
      case 'table':
        return await this.createTableWidget(config);
      case 'alert':
        return await this.createAlertWidget(config);
      default:
        throw new Error(`不支持的组件类型: ${config.type}`);
    }
  }

  /**
   * 创建图表组件
   */
  private async createChartWidget(config: WidgetConfig): Promise<any> {
    // 根据配置创建图表
    const chartConfig = await this.createProductionEfficiencyChart(
      new Date(Date.now() - 24 * 60 * 60 * 1000),
      new Date()
    );

    return {
      id: config.id,
      type: 'chart',
      title: config.title,
      position: config.position,
      content: chartConfig
    };
  }

  /**
   * 创建KPI组件
   */
  private async createKPIWidget(config: WidgetConfig): Promise<any> {
    // 模拟KPI数据
    const kpiData = {
      value: 85.6,
      unit: '%',
      trend: 'up',
      change: '+2.3%'
    };

    return {
      id: config.id,
      type: 'kpi',
      title: config.title,
      position: config.position,
      content: kpiData
    };
  }

  /**
   * 创建表格组件
   */
  private async createTableWidget(config: WidgetConfig): Promise<any> {
    // 模拟表格数据
    const tableData = {
      headers: ['设备ID', '状态', '效率', '质量'],
      rows: [
        ['device-001', '正常', '85%', '98%'],
        ['device-002', '警告', '78%', '95%'],
        ['device-003', '正常', '92%', '99%']
      ]
    };

    return {
      id: config.id,
      type: 'table',
      title: config.title,
      position: config.position,
      content: tableData
    };
  }

  /**
   * 创建警报组件
   */
  private async createAlertWidget(config: WidgetConfig): Promise<any> {
    // 模拟警报数据
    const alertData = {
      alerts: [
        { level: 'warning', message: '设备温度偏高', time: '10:30' },
        { level: 'info', message: '维护提醒', time: '09:15' }
      ]
    };

    return {
      id: config.id,
      type: 'alert',
      title: config.title,
      position: config.position,
      content: alertData
    };
  }

  /**
   * 获取生产数据
   */
  private async getProductionData(startDate: Date, endDate: Date, deviceIds?: string[]): Promise<any[]> {
    // 模拟数据生成
    const data: any[] = [];
    const devices = deviceIds || ['device-001', 'device-002', 'device-003'];

    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      devices.forEach(deviceId => {
        data.push({
          timestamp: current.toDate(),
          deviceId,
          deviceType: 'CNC_MACHINE',
          productionLine: 'LINE_001',
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          downtime: Math.random() * 30,
          energyConsumption: 50 + Math.random() * 20,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5
        });
      });
      current.add(1, 'hour');
    }

    return data;
  }

  /**
   * 计算可用性
   */
  private calculateAvailability(data: any[]): number {
    const totalTime = data.length;
    const downtime = data.reduce((sum, d) => sum + d.downtime, 0);
    return totalTime > 0 ? ((totalTime * 60 - downtime) / (totalTime * 60)) * 100 : 0;
  }
}