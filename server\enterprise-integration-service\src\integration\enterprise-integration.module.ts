import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';

// 导入控制器和服务
import { EnterpriseIntegrationController } from './enterprise-integration.controller';
import { EnterpriseIntegrationService } from './enterprise-integration.service';

// 新增服务
import { ERPIntegrationService } from './services/erp-integration.service';
import { DataSyncService } from './services/data-sync.service';
import { APIGatewayService } from './services/api-gateway.service';
import { AuthenticationService } from './services/authentication.service';

// 导入实体类
import { EnterpriseSystemEntity } from './entities/enterprise-system.entity';
import { IntegrationFlowEntity } from './entities/integration-flow.entity';
import { SyncTaskEntity } from './entities/sync-task.entity';
import { SyncErrorEntity } from './entities/sync-error.entity';
import { APIEndpointEntity } from './entities/api-endpoint.entity';
import { AuthTokenEntity } from './entities/auth-token.entity';

// 导入网关
import { EnterpriseIntegrationGateway } from './enterprise-integration.gateway';

/**
 * 企业系统深度集成模块
 * 提供企业系统注册、集成流程管理、数据同步等功能
 */
@Module({
  imports: [
    // TypeORM实体注册
    TypeOrmModule.forFeature([
      EnterpriseSystemEntity,
      IntegrationFlowEntity,
      SyncTaskEntity,
      SyncErrorEntity,
      APIEndpointEntity,
      AuthTokenEntity,
    ]),

    // 缓存模块配置
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ttl: configService.get<number>('CACHE_TTL', 300), // 5分钟
        max: configService.get<number>('CACHE_MAX_ITEMS', 1000),
        store: 'memory',
      }),
      inject: [ConfigService],
    }),

    // 队列模块配置（用于异步任务处理）
    BullModule.registerQueueAsync({
      name: 'enterprise-integration',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD'),
          db: configService.get<number>('REDIS_DB', 0),
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [EnterpriseIntegrationController],
  providers: [
    EnterpriseIntegrationService,
    EnterpriseIntegrationGateway,
    ERPIntegrationService,
    DataSyncService,
    APIGatewayService,
    AuthenticationService,
  ],
  exports: [
    EnterpriseIntegrationService,
    TypeOrmModule,
  ],
})
export class EnterpriseIntegrationModule {}
