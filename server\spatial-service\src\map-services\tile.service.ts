/**
 * 瓦片服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';

@Injectable()
export class TileService {
  private readonly logger = new Logger(TileService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
  ) {}

  /**
   * 获取地图瓦片
   */
  async getTile(z: number, x: number, y: number, layerId?: string): Promise<Buffer> {
    try {
      this.logger.log(`获取瓦片: z=${z}, x=${x}, y=${y}, layer=${layerId}`);

      // 计算瓦片边界
      const bbox = this.tileToBBox(x, y, z);
      
      if (layerId) {
        // 获取指定图层的瓦片
        return await this.generateLayerTile(layerId, bbox, z);
      } else {
        // 获取默认底图瓦片
        return await this.generateBaseTile(bbox, z);
      }

    } catch (error) {
      this.logger.error(`瓦片生成失败: ${error.message}`);
      // 返回空白瓦片
      return this.generateEmptyTile();
    }
  }

  /**
   * 计算瓦片边界框
   */
  private tileToBBox(x: number, y: number, z: number): [number, number, number, number] {
    const n = Math.pow(2, z);
    const lonMin = (x / n) * 360 - 180;
    const lonMax = ((x + 1) / n) * 360 - 180;
    const latMin = Math.atan(Math.sinh(Math.PI * (1 - 2 * (y + 1) / n))) * 180 / Math.PI;
    const latMax = Math.atan(Math.sinh(Math.PI * (1 - 2 * y / n))) * 180 / Math.PI;
    
    return [lonMin, latMin, lonMax, latMax];
  }

  /**
   * 生成图层瓦片
   */
  private async generateLayerTile(layerId: string, bbox: [number, number, number, number], zoom: number): Promise<Buffer> {
    // 查询瓦片范围内的要素
    const features = await this.spatialFeatureRepository
      .createQueryBuilder('feature')
      .where('feature.layerId = :layerId', { layerId })
      .andWhere(`
        ST_Intersects(
          feature.geometry::geometry,
          ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326)
        )
      `, {
        minX: bbox[0],
        minY: bbox[1],
        maxX: bbox[2],
        maxY: bbox[3]
      })
      .getMany();

    // 获取图层样式
    const layer = await this.spatialLayerRepository.findOne({
      where: { id: layerId }
    });

    // 生成瓦片图像
    return this.renderTile(features, layer?.style, bbox, zoom);
  }

  /**
   * 生成底图瓦片
   */
  private async generateBaseTile(bbox: [number, number, number, number], zoom: number): Promise<Buffer> {
    // 这里可以集成第三方底图服务
    // 或者生成简单的底图
    return this.generateSimpleBaseTile(bbox, zoom);
  }

  /**
   * 渲染瓦片
   */
  private async renderTile(
    features: SpatialFeature[], 
    style: any, 
    bbox: [number, number, number, number], 
    zoom: number
  ): Promise<Buffer> {
    // 简化实现：生成一个包含要素信息的PNG瓦片
    // 实际应用中应该使用专业的地图渲染库如Mapnik
    
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 设置背景
    ctx.fillStyle = '#f8f8f8';
    ctx.fillRect(0, 0, tileSize, tileSize);

    // 绘制要素
    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        this.drawGeometry(ctx, geometry, style, bbox, tileSize);
      } catch (error) {
        this.logger.warn(`绘制要素失败: ${feature.id}`, error);
      }
    }

    // 转换为PNG Buffer
    return canvas.toBuffer('image/png');
  }

  /**
   * 创建画布
   */
  private createCanvas(width: number, height: number): any {
    // 这里需要使用canvas库，如node-canvas
    // 简化实现，返回模拟对象
    return {
      getContext: () => ({
        fillStyle: '',
        strokeStyle: '',
        lineWidth: 1,
        fillRect: () => {},
        strokeRect: () => {},
        beginPath: () => {},
        moveTo: () => {},
        lineTo: () => {},
        closePath: () => {},
        fill: () => {},
        stroke: () => {}
      }),
      toBuffer: () => Buffer.alloc(0)
    };
  }

  /**
   * 绘制几何图形
   */
  private drawGeometry(ctx: any, geometry: any, style: any, bbox: [number, number, number, number], tileSize: number) {
    const coords = this.projectCoordinates(geometry.coordinates, bbox, tileSize);
    
    // 设置样式
    if (style?.fill?.color) {
      ctx.fillStyle = style.fill.color;
    }
    if (style?.stroke?.color) {
      ctx.strokeStyle = style.stroke.color;
      ctx.lineWidth = style.stroke.width || 1;
    }

    switch (geometry.type) {
      case 'Point':
        this.drawPoint(ctx, coords);
        break;
      case 'LineString':
        this.drawLineString(ctx, coords);
        break;
      case 'Polygon':
        this.drawPolygon(ctx, coords);
        break;
      case 'MultiPoint':
        coords.forEach(coord => this.drawPoint(ctx, coord));
        break;
      case 'MultiLineString':
        coords.forEach(coord => this.drawLineString(ctx, coord));
        break;
      case 'MultiPolygon':
        coords.forEach(coord => this.drawPolygon(ctx, coord));
        break;
    }
  }

  /**
   * 投影坐标到瓦片像素坐标
   */
  private projectCoordinates(coordinates: any, bbox: [number, number, number, number], tileSize: number): any {
    const [minX, minY, maxX, maxY] = bbox;
    const scaleX = tileSize / (maxX - minX);
    const scaleY = tileSize / (maxY - minY);

    const projectPoint = (coord: [number, number]) => [
      (coord[0] - minX) * scaleX,
      tileSize - (coord[1] - minY) * scaleY // 翻转Y轴
    ];

    if (Array.isArray(coordinates[0])) {
      if (Array.isArray(coordinates[0][0])) {
        // 多维数组
        return coordinates.map(ring => ring.map(projectPoint));
      } else {
        // 二维数组
        return coordinates.map(projectPoint);
      }
    } else {
      // 一维数组（点坐标）
      return projectPoint(coordinates);
    }
  }

  /**
   * 绘制点
   */
  private drawPoint(ctx: any, coords: [number, number]) {
    const radius = 3;
    ctx.beginPath();
    ctx.arc(coords[0], coords[1], radius, 0, 2 * Math.PI);
    ctx.fill();
  }

  /**
   * 绘制线
   */
  private drawLineString(ctx: any, coords: [number, number][]) {
    if (coords.length < 2) return;

    ctx.beginPath();
    ctx.moveTo(coords[0][0], coords[0][1]);
    
    for (let i = 1; i < coords.length; i++) {
      ctx.lineTo(coords[i][0], coords[i][1]);
    }
    
    ctx.stroke();
  }

  /**
   * 绘制多边形
   */
  private drawPolygon(ctx: any, coords: [number, number][][]) {
    if (coords.length === 0) return;

    // 绘制外环
    const exterior = coords[0];
    if (exterior.length < 3) return;

    ctx.beginPath();
    ctx.moveTo(exterior[0][0], exterior[0][1]);
    
    for (let i = 1; i < exterior.length; i++) {
      ctx.lineTo(exterior[i][0], exterior[i][1]);
    }
    
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // 绘制内环（洞）
    for (let i = 1; i < coords.length; i++) {
      const hole = coords[i];
      if (hole.length < 3) continue;

      ctx.beginPath();
      ctx.moveTo(hole[0][0], hole[0][1]);
      
      for (let j = 1; j < hole.length; j++) {
        ctx.lineTo(hole[j][0], hole[j][1]);
      }
      
      ctx.closePath();
      // 使用复合操作创建洞
      ctx.globalCompositeOperation = 'destination-out';
      ctx.fill();
      ctx.globalCompositeOperation = 'source-over';
    }
  }

  /**
   * 生成简单底图瓦片
   */
  private generateSimpleBaseTile(bbox: [number, number, number, number], zoom: number): Buffer {
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 绘制简单的网格底图
    ctx.fillStyle = '#e6f3ff';
    ctx.fillRect(0, 0, tileSize, tileSize);

    // 绘制网格线
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;

    const gridSize = 32;
    for (let i = 0; i <= tileSize; i += gridSize) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, tileSize);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(tileSize, i);
      ctx.stroke();
    }

    return canvas.toBuffer('image/png');
  }

  /**
   * 生成空白瓦片
   */
  private generateEmptyTile(): Buffer {
    const tileSize = 256;
    const canvas = this.createCanvas(tileSize, tileSize);
    const ctx = canvas.getContext('2d');

    // 透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0)';
    ctx.fillRect(0, 0, tileSize, tileSize);

    return canvas.toBuffer('image/png');
  }

  /**
   * 获取瓦片缓存键
   */
  private getTileCacheKey(z: number, x: number, y: number, layerId?: string): string {
    return `tile:${z}:${x}:${y}:${layerId || 'base'}`;
  }

  /**
   * 获取矢量瓦片 (MVT)
   */
  async getVectorTile(z: number, x: number, y: number, layerId?: string): Promise<Buffer> {
    try {
      this.logger.log(`获取矢量瓦片: z=${z}, x=${x}, y=${y}, layer=${layerId}`);

      this.validateTileCoordinates(z, x, y);
      const bbox = this.tileToBBox(x, y, z);

      // 查询瓦片范围内的要素
      const features = await this.getFeaturesInBounds(bbox, layerId);

      // 生成MVT格式的瓦片
      return this.generateMVTTile(features, z, x, y);

    } catch (error) {
      this.logger.error(`矢量瓦片生成失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取瓦片元数据
   */
  async getTileMetadata(layerId?: string): Promise<any> {
    try {
      if (layerId) {
        const layer = await this.spatialLayerRepository.findOne({
          where: { id: layerId }
        });

        if (!layer) {
          throw new Error('图层不存在');
        }

        const bounds = layer.metadata?.extent || [-180, -90, 180, 90];
        const center = [
          (bounds[0] + bounds[2]) / 2,
          (bounds[1] + bounds[3]) / 2,
          10
        ];

        return {
          name: layer.name,
          description: layer.description || '',
          version: '1.0.0',
          minzoom: 0,
          maxzoom: 18,
          bounds: bounds,
          center: center,
          format: 'png',
          type: 'overlay'
        };
      } else {
        // 默认底图元数据
        return {
          name: 'Base Map',
          description: 'Default base map tiles',
          version: '1.0.0',
          minzoom: 0,
          maxzoom: 18,
          bounds: [-180, -90, 180, 90],
          center: [0, 0, 2],
          format: 'png',
          type: 'baselayer'
        };
      }
    } catch (error) {
      this.logger.error(`获取瓦片元数据失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证瓦片坐标
   */
  private validateTileCoordinates(z: number, x: number, y: number): void {
    if (z < 0 || z > 18) {
      throw new Error(`无效的缩放级别: ${z}`);
    }

    const maxTile = Math.pow(2, z);
    if (x < 0 || x >= maxTile || y < 0 || y >= maxTile) {
      throw new Error(`无效的瓦片坐标: ${x}, ${y} at zoom ${z}`);
    }
  }

  /**
   * 获取边界框内的要素
   */
  private async getFeaturesInBounds(bbox: [number, number, number, number], layerId?: string): Promise<SpatialFeature[]> {
    const query = this.spatialFeatureRepository
      .createQueryBuilder('feature')
      .where(`
        ST_Intersects(
          feature.geometry::geometry,
          ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326)
        )
      `, {
        minX: bbox[0],
        minY: bbox[1],
        maxX: bbox[2],
        maxY: bbox[3]
      });

    if (layerId) {
      query.andWhere('feature.layerId = :layerId', { layerId });
    }

    return await query.getMany();
  }

  /**
   * 生成MVT瓦片
   */
  private generateMVTTile(features: SpatialFeature[], z: number, x: number, y: number): Buffer {
    // 简化实现：生成GeoJSON格式的数据
    // 实际应用中应该使用专业的MVT编码库
    const geojson = {
      type: 'FeatureCollection',
      features: features.map(feature => ({
        type: 'Feature',
        id: feature.id,
        geometry: JSON.parse(feature.geometry),
        properties: {
          ...feature.properties,
          name: feature.name,
          featureType: feature.featureType
        }
      }))
    };

    return Buffer.from(JSON.stringify(geojson), 'utf-8');
  }

  /**
   * 获取瓦片统计信息
   */
  async getTileStats(layerId?: string): Promise<any> {
    const query = this.spatialFeatureRepository.createQueryBuilder('feature');

    if (layerId) {
      query.where('feature.layerId = :layerId', { layerId });
    }

    const [featureCount, layer] = await Promise.all([
      query.getCount(),
      layerId ? this.spatialLayerRepository.findOne({ where: { id: layerId } }) : null
    ]);

    return {
      featureCount,
      layerName: layer?.name || 'All Layers',
      tileFormat: ['png', 'jpg', 'webp', 'mvt'],
      supportedZoomLevels: { min: 0, max: 18 },
      cacheEnabled: true,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 清除瓦片缓存
   */
  async clearTileCache(layerId?: string) {
    // 这里应该实现缓存清除逻辑
    this.logger.log(`清除瓦片缓存: ${layerId || 'all'}`);
  }

  /**
   * 预生成瓦片
   */
  async preGenerateTiles(layerId: string, zoomLevels: number[], bounds?: [number, number, number, number]): Promise<{ success: boolean; tilesGenerated: number }> {
    this.logger.log(`预生成瓦片: layer=${layerId}, zooms=${zoomLevels.join(',')}`);

    let tilesGenerated = 0;

    for (const zoom of zoomLevels) {
      const tiles = this.getTilesInBounds(bounds || [-180, -90, 180, 90], zoom);

      for (const tile of tiles) {
        try {
          await this.getTile(tile.z, tile.x, tile.y, layerId);
          tilesGenerated++;
        } catch (error) {
          this.logger.warn(`预生成瓦片失败: ${tile.z}/${tile.x}/${tile.y}`, error);
        }
      }
    }

    return {
      success: true,
      tilesGenerated
    };
  }

  /**
   * 获取边界框内的瓦片列表
   */
  private getTilesInBounds(bounds: [number, number, number, number], zoom: number): Array<{ z: number; x: number; y: number }> {
    const tiles = [];
    const [minLon, minLat, maxLon, maxLat] = bounds;

    const minTileX = Math.floor((minLon + 180) / 360 * Math.pow(2, zoom));
    const maxTileX = Math.floor((maxLon + 180) / 360 * Math.pow(2, zoom));
    const minTileY = Math.floor((1 - Math.log(Math.tan(maxLat * Math.PI / 180) + 1 / Math.cos(maxLat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));
    const maxTileY = Math.floor((1 - Math.log(Math.tan(minLat * Math.PI / 180) + 1 / Math.cos(minLat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));

    for (let x = minTileX; x <= maxTileX; x++) {
      for (let y = minTileY; y <= maxTileY; y++) {
        tiles.push({ z: zoom, x, y });
      }
    }

    return tiles;
  }
}
