import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * 认证令牌实体
 */
@Entity('auth_tokens')
@Index(['userId', 'tokenType'])
@Index(['systemId', 'status'])
@Index(['expiresAt'])
export class AuthTokenEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, comment: '用户ID' })
  userId: string;

  @Column({ type: 'varchar', length: 100, comment: '系统ID' })
  systemId: string;

  @Column({ type: 'varchar', length: 50, comment: '令牌类型' })
  tokenType: 'access_token' | 'refresh_token' | 'api_key' | 'session_token';

  @Column({ type: 'text', comment: '令牌值' })
  tokenValue: string;

  @Column({ type: 'text', nullable: true, comment: '刷新令牌' })
  refreshToken?: string;

  @Column({ type: 'varchar', length: 50, comment: '状态' })
  status: 'active' | 'expired' | 'revoked' | 'suspended';

  @Column({ type: 'timestamp', comment: '过期时间' })
  expiresAt: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '刷新令牌过期时间' })
  refreshExpiresAt?: Date;

  @Column({ type: 'json', nullable: true, comment: '权限范围' })
  scopes?: string[];

  @Column({ type: 'json', nullable: true, comment: '额外属性' })
  metadata?: any;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '客户端ID' })
  clientId?: string;

  @Column({ type: 'varchar', length: 200, nullable: true, comment: '客户端名称' })
  clientName?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: 'IP地址' })
  ipAddress?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '用户代理' })
  userAgent?: string;

  @Column({ type: 'timestamp', nullable: true, comment: '最后使用时间' })
  lastUsedAt?: Date;

  @Column({ type: 'int', default: 0, comment: '使用次数' })
  usageCount: number;

  @Column({ type: 'boolean', default: false, comment: '是否为长期令牌' })
  isLongTerm: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '创建者' })
  createdBy?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '撤销者' })
  revokedBy?: string;

  @Column({ type: 'timestamp', nullable: true, comment: '撤销时间' })
  revokedAt?: Date;

  @Column({ type: 'varchar', length: 200, nullable: true, comment: '撤销原因' })
  revokeReason?: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  notes?: string;

  /**
   * 检查令牌是否有效
   */
  isValid(): boolean {
    return this.status === 'active' && new Date() < this.expiresAt;
  }

  /**
   * 检查刷新令牌是否有效
   */
  isRefreshTokenValid(): boolean {
    return this.refreshToken && 
           this.refreshExpiresAt && 
           new Date() < this.refreshExpiresAt &&
           this.status === 'active';
  }

  /**
   * 检查是否即将过期（30分钟内）
   */
  isExpiringSoon(): boolean {
    const thirtyMinutesFromNow = new Date(Date.now() + 30 * 60 * 1000);
    return this.expiresAt < thirtyMinutesFromNow;
  }

  /**
   * 检查是否有特定权限
   */
  hasScope(scope: string): boolean {
    return this.scopes ? this.scopes.includes(scope) : false;
  }

  /**
   * 更新使用统计
   */
  updateUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  /**
   * 撤销令牌
   */
  revoke(revokedBy: string, reason?: string): void {
    this.status = 'revoked';
    this.revokedBy = revokedBy;
    this.revokedAt = new Date();
    this.revokeReason = reason;
  }
}
