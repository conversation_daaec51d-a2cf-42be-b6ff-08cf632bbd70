/**
 * 高级GIS服务
 * 提供复杂的空间数据库操作和高级GIS功能
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';
import * as turf from '@turf/turf';

export interface SpatialQueryOptions {
  geometry?: any;
  bbox?: [number, number, number, number];
  relation?: 'intersects' | 'contains' | 'within' | 'touches' | 'crosses' | 'overlaps';
  distance?: number;
  unit?: string;
  layerIds?: string[];
  featureTypes?: string[];
  attributes?: Record<string, any>;
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export interface SpatialIndexOptions {
  indexType?: 'gist' | 'spgist' | 'gin';
  indexMethod?: 'btree' | 'hash' | 'gist' | 'spgist' | 'gin' | 'brin';
  tablespace?: string;
  fillfactor?: number;
}

export interface TopologyOptions {
  tolerance?: number;
  preserveCollapsed?: boolean;
  modifyInPlace?: boolean;
}

@Injectable()
export class AdvancedGISService {
  private readonly logger = new Logger(AdvancedGISService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
    @InjectRepository(SpatialProject)
    private spatialProjectRepository: Repository<SpatialProject>,
  ) {}

  /**
   * 高级空间查询
   */
  async advancedSpatialQuery(options: SpatialQueryOptions): Promise<any> {
    this.logger.log(`执行高级空间查询: ${JSON.stringify(options)}`);

    const queryBuilder = this.spatialFeatureRepository.createQueryBuilder('feature');

    // 空间关系查询
    if (options.geometry) {
      const geometryWKT = this.geometryToWKT(options.geometry);
      const relation = options.relation || 'intersects';
      
      switch (relation) {
        case 'intersects':
          queryBuilder.andWhere('ST_Intersects(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
        case 'contains':
          queryBuilder.andWhere('ST_Contains(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
        case 'within':
          queryBuilder.andWhere('ST_Within(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
        case 'touches':
          queryBuilder.andWhere('ST_Touches(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
        case 'crosses':
          queryBuilder.andWhere('ST_Crosses(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
        case 'overlaps':
          queryBuilder.andWhere('ST_Overlaps(feature.geometry, ST_GeomFromText(:geom, 4326))', { geom: geometryWKT });
          break;
      }
    }

    // 边界框查询
    if (options.bbox) {
      const [minX, minY, maxX, maxY] = options.bbox;
      queryBuilder.andWhere(
        'ST_Intersects(feature.geometry, ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326))',
        { minX, minY, maxX, maxY }
      );
    }

    // 距离查询
    if (options.distance && options.geometry) {
      const geometryWKT = this.geometryToWKT(options.geometry);
      const unit = options.unit || 'meters';
      queryBuilder.andWhere(
        'ST_DWithin(feature.geometry, ST_GeomFromText(:geom, 4326), :distance)',
        { geom: geometryWKT, distance: options.distance }
      );
    }

    // 图层过滤
    if (options.layerIds && options.layerIds.length > 0) {
      queryBuilder.andWhere('feature.layerId IN (:...layerIds)', { layerIds: options.layerIds });
    }

    // 要素类型过滤
    if (options.featureTypes && options.featureTypes.length > 0) {
      queryBuilder.andWhere('feature.featureType IN (:...featureTypes)', { featureTypes: options.featureTypes });
    }

    // 属性过滤
    if (options.attributes) {
      for (const [key, value] of Object.entries(options.attributes)) {
        queryBuilder.andWhere(`feature.properties->>'${key}' = :${key}`, { [key]: value });
      }
    }

    // 排序
    if (options.orderBy) {
      const direction = options.orderDirection || 'ASC';
      queryBuilder.orderBy(`feature.${options.orderBy}`, direction);
    }

    // 分页
    if (options.limit) {
      queryBuilder.limit(options.limit);
    }
    if (options.offset) {
      queryBuilder.offset(options.offset);
    }

    const features = await queryBuilder.getMany();
    const total = await queryBuilder.getCount();

    return {
      success: true,
      features,
      total,
      query: options
    };
  }

  /**
   * 空间索引管理
   */
  async createSpatialIndex(tableName: string, columnName: string, options: SpatialIndexOptions = {}): Promise<any> {
    this.logger.log(`创建空间索引: ${tableName}.${columnName}`);

    const indexType = options.indexType || 'gist';
    const indexName = `idx_${tableName}_${columnName}_${indexType}`;

    let sql = `CREATE INDEX ${indexName} ON ${tableName} USING ${indexType} (${columnName})`;

    if (options.tablespace) {
      sql += ` TABLESPACE ${options.tablespace}`;
    }

    if (options.fillfactor) {
      sql += ` WITH (fillfactor = ${options.fillfactor})`;
    }

    try {
      await this.spatialFeatureRepository.query(sql);
      return {
        success: true,
        indexName,
        message: '空间索引创建成功'
      };
    } catch (error) {
      this.logger.error(`创建空间索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 空间索引统计
   */
  async getSpatialIndexStats(tableName: string): Promise<any> {
    this.logger.log(`获取空间索引统计: ${tableName}`);

    const sql = `
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_blks_read,
        idx_blks_hit
      FROM pg_stat_user_indexes 
      WHERE tablename = $1
      AND indexname LIKE '%gist%' OR indexname LIKE '%spgist%'
    `;

    try {
      const stats = await this.spatialFeatureRepository.query(sql, [tableName]);
      return {
        success: true,
        stats
      };
    } catch (error) {
      this.logger.error(`获取空间索引统计失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 拓扑验证
   */
  async validateTopology(layerId: string, options: TopologyOptions = {}): Promise<any> {
    this.logger.log(`验证拓扑: ${layerId}`);

    const tolerance = options.tolerance || 0.0001;
    const features = await this.spatialFeatureRepository.find({
      where: { layerId }
    });

    const validationResults = [];

    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        const validation = {
          featureId: feature.id,
          isValid: true,
          errors: []
        };

        // 检查几何有效性
        const isValid = turf.booleanValid(geometry);
        if (!isValid) {
          validation.isValid = false;
          validation.errors.push('几何图形无效');
        }

        // 检查自相交
        if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
          const kinks = turf.kinks(geometry);
          if (kinks.features.length > 0) {
            validation.isValid = false;
            validation.errors.push(`发现${kinks.features.length}个自相交点`);
          }
        }

        validationResults.push(validation);

      } catch (error) {
        validationResults.push({
          featureId: feature.id,
          isValid: false,
          errors: [`验证失败: ${error.message}`]
        });
      }
    }

    const invalidCount = validationResults.filter(r => !r.isValid).length;

    return {
      success: true,
      totalFeatures: features.length,
      validFeatures: features.length - invalidCount,
      invalidFeatures: invalidCount,
      validationResults
    };
  }

  /**
   * 拓扑清理
   */
  async cleanTopology(layerId: string, options: TopologyOptions = {}): Promise<any> {
    this.logger.log(`清理拓扑: ${layerId}`);

    const tolerance = options.tolerance || 0.0001;
    const features = await this.spatialFeatureRepository.find({
      where: { layerId }
    });

    const cleanedFeatures = [];

    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        let cleanedGeometry = geometry;

        // 简化几何图形
        if (tolerance > 0) {
          cleanedGeometry = turf.simplify(cleanedGeometry, { tolerance, highQuality: true });
        }

        // 修复无效几何图形
        if (!turf.booleanValid(cleanedGeometry)) {
          // 尝试使用buffer(0)修复
          cleanedGeometry = turf.buffer(cleanedGeometry, 0);
        }

        // 更新要素
        if (options.modifyInPlace) {
          feature.geometry = JSON.stringify(cleanedGeometry);
          await this.spatialFeatureRepository.save(feature);
        }

        cleanedFeatures.push({
          featureId: feature.id,
          originalGeometry: geometry,
          cleanedGeometry,
          modified: JSON.stringify(geometry) !== JSON.stringify(cleanedGeometry)
        });

      } catch (error) {
        this.logger.error(`清理要素失败: ${feature.id}`, error);
        cleanedFeatures.push({
          featureId: feature.id,
          error: error.message
        });
      }
    }

    const modifiedCount = cleanedFeatures.filter(f => f.modified).length;

    return {
      success: true,
      totalFeatures: features.length,
      modifiedFeatures: modifiedCount,
      cleanedFeatures
    };
  }

  /**
   * 几何图形转WKT
   */
  private geometryToWKT(geometry: any): string {
    // 简化实现，实际应用中应该使用专业的WKT转换库
    if (geometry.type === 'Point') {
      const [x, y] = geometry.coordinates;
      return `POINT(${x} ${y})`;
    } else if (geometry.type === 'Polygon') {
      const coords = geometry.coordinates[0];
      const coordStr = coords.map(c => `${c[0]} ${c[1]}`).join(', ');
      return `POLYGON((${coordStr}))`;
    }
    // 其他几何类型的处理...
    return 'POINT(0 0)'; // 默认值
  }
}
