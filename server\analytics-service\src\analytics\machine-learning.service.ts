import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as ss from 'simple-statistics';
import { SLR, MLR, PolynomialRegression } from 'ml-regression';
import moment from 'moment';
import { ProductionDataEntity } from '../entities/production-data.entity';
import { AnalysisResultEntity } from '../entities/analysis-result.entity';

/**
 * ML模型接口
 */
interface MLModel {
  id: string;
  name: string;
  type: 'regression' | 'classification' | 'clustering' | 'anomaly_detection';
  algorithm: string;
  features: string[];
  target: string;
  model: any;
  accuracy: number;
  trainedAt: Date;
  version: number;
}

/**
 * 预测结果接口
 */
interface PredictionResult {
  modelId: string;
  input: any;
  prediction: number | string;
  confidence: number;
  timestamp: Date;
  features: { [key: string]: number };
}

/**
 * 模型训练配置接口
 */
interface TrainingConfig {
  modelType: 'regression' | 'classification' | 'clustering';
  algorithm: string;
  features: string[];
  target: string;
  trainingData: any[];
  validationSplit: number;
  hyperparameters?: any;
}

/**
 * 异常检测结果接口
 */
interface AnomalyResult {
  timestamp: Date;
  deviceId: string;
  features: { [key: string]: number };
  anomalyScore: number;
  isAnomaly: boolean;
  threshold: number;
  explanation: string;
}

/**
 * 机器学习服务
 */
@Injectable()
export class MachineLearningService {
  private readonly logger = new Logger(MachineLearningService.name);
  
  // 模型存储
  private readonly models: Map<string, MLModel> = new Map();
  
  // 预测缓存
  private readonly predictionCache: Map<string, PredictionResult[]> = new Map();
  
  // 异常检测阈值
  private readonly anomalyThresholds: Map<string, number> = new Map();

  constructor(
    @InjectRepository(ProductionDataEntity)
    private productionDataRepository: Repository<ProductionDataEntity>,
    @InjectRepository(AnalysisResultEntity)
    private analysisResultRepository: Repository<AnalysisResultEntity>
  ) {
    this.initializeModels();
  }

  /**
   * 训练预测模型
   */
  async trainPredictionModel(config: TrainingConfig): Promise<MLModel> {
    try {
      this.logger.log(`开始训练模型: ${config.algorithm}`);

      // 数据预处理
      const processedData = this.preprocessData(config.trainingData, config.features, config.target);
      
      // 分割训练和验证数据
      const splitIndex = Math.floor(processedData.length * (1 - config.validationSplit));
      const trainData = processedData.slice(0, splitIndex);
      const validData = processedData.slice(splitIndex);

      // 训练模型
      let model: any;
      let accuracy: number;

      switch (config.algorithm) {
        case 'linear_regression':
          model = this.trainLinearRegression(trainData, config.features, config.target);
          accuracy = this.evaluateRegressionModel(model, validData, config.features, config.target);
          break;
        case 'polynomial_regression':
          model = this.trainPolynomialRegression(trainData, config.features, config.target, config.hyperparameters?.degree || 2);
          accuracy = this.evaluateRegressionModel(model, validData, config.features, config.target);
          break;
        case 'multiple_regression':
          model = this.trainMultipleRegression(trainData, config.features, config.target);
          accuracy = this.evaluateRegressionModel(model, validData, config.features, config.target);
          break;
        default:
          throw new Error(`不支持的算法: ${config.algorithm}`);
      }

      // 创建模型对象
      const mlModel: MLModel = {
        id: `model_${Date.now()}`,
        name: `${config.algorithm}_${config.target}`,
        type: config.modelType,
        algorithm: config.algorithm,
        features: config.features,
        target: config.target,
        model,
        accuracy,
        trainedAt: new Date(),
        version: 1
      };

      // 存储模型
      this.models.set(mlModel.id, mlModel);

      this.logger.log(`模型训练完成: ${mlModel.id}, 准确率: ${accuracy.toFixed(4)}`);
      return mlModel;

    } catch (error) {
      this.logger.error('训练模型失败', error);
      throw error;
    }
  }

  /**
   * 执行预测
   */
  async predict(modelId: string, input: any): Promise<PredictionResult> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        throw new Error(`模型不存在: ${modelId}`);
      }

      // 提取特征
      const features = this.extractFeatures(input, model.features);
      
      // 执行预测
      let prediction: number | string;
      let confidence: number;

      switch (model.algorithm) {
        case 'linear_regression':
        case 'polynomial_regression':
          prediction = model.model.predict(features);
          confidence = this.calculateRegressionConfidence(model, features);
          break;
        case 'multiple_regression':
          prediction = model.model.predict(features);
          confidence = this.calculateRegressionConfidence(model, features);
          break;
        default:
          throw new Error(`不支持的预测算法: ${model.algorithm}`);
      }

      const result: PredictionResult = {
        modelId,
        input,
        prediction,
        confidence,
        timestamp: new Date(),
        features: this.createFeatureMap(model.features, features)
      };

      // 缓存预测结果
      this.cachePrediction(modelId, result);

      return result;

    } catch (error) {
      this.logger.error('预测失败', error);
      throw error;
    }
  }

  /**
   * 批量预测
   */
  async batchPredict(modelId: string, inputs: any[]): Promise<PredictionResult[]> {
    const results: PredictionResult[] = [];
    
    for (const input of inputs) {
      try {
        const result = await this.predict(modelId, input);
        results.push(result);
      } catch (error) {
        this.logger.warn(`批量预测中的单个预测失败: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * 异常检测
   */
  async detectAnomalies(
    data: any[],
    features: string[],
    method: 'statistical' | 'isolation_forest' | 'one_class_svm' = 'statistical'
  ): Promise<AnomalyResult[]> {
    try {
      const results: AnomalyResult[] = [];

      switch (method) {
        case 'statistical':
          return this.statisticalAnomalyDetection(data, features);
        case 'isolation_forest':
          return this.isolationForestAnomalyDetection(data, features);
        case 'one_class_svm':
          return this.oneClassSVMAnomalyDetection(data, features);
        default:
          throw new Error(`不支持的异常检测方法: ${method}`);
      }

    } catch (error) {
      this.logger.error('异常检测失败', error);
      throw error;
    }
  }

  /**
   * 特征重要性分析
   */
  async analyzeFeatureImportance(
    data: any[],
    features: string[],
    target: string
  ): Promise<{ [feature: string]: number }> {
    try {
      const importance: { [feature: string]: number } = {};

      // 计算每个特征与目标变量的相关性
      for (const feature of features) {
        const featureValues = data.map(d => d[feature]).filter(v => v !== undefined && v !== null);
        const targetValues = data.map(d => d[target]).filter(v => v !== undefined && v !== null);
        
        if (featureValues.length === targetValues.length && featureValues.length > 1) {
          const correlation = Math.abs(ss.sampleCorrelation(featureValues, targetValues));
          importance[feature] = correlation;
        } else {
          importance[feature] = 0;
        }
      }

      // 归一化重要性分数
      const maxImportance = Math.max(...Object.values(importance));
      if (maxImportance > 0) {
        Object.keys(importance).forEach(feature => {
          importance[feature] = importance[feature] / maxImportance;
        });
      }

      this.logger.log('特征重要性分析完成');
      return importance;

    } catch (error) {
      this.logger.error('特征重要性分析失败', error);
      throw error;
    }
  }

  /**
   * 模型性能评估
   */
  async evaluateModel(modelId: string, testData: any[]): Promise<any> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        throw new Error(`模型不存在: ${modelId}`);
      }

      const predictions: number[] = [];
      const actuals: number[] = [];

      for (const data of testData) {
        const features = this.extractFeatures(data, model.features);
        const prediction = model.model.predict(features);
        const actual = data[model.target];

        if (actual !== undefined && actual !== null) {
          predictions.push(prediction);
          actuals.push(actual);
        }
      }

      // 计算评估指标
      const metrics = this.calculateRegressionMetrics(predictions, actuals);

      this.logger.log(`模型评估完成: ${modelId}`);
      return {
        modelId,
        testSamples: predictions.length,
        metrics,
        evaluatedAt: new Date()
      };

    } catch (error) {
      this.logger.error('模型评估失败', error);
      throw error;
    }
  }

  /**
   * 获取模型列表
   */
  getModels(): MLModel[] {
    return Array.from(this.models.values());
  }

  /**
   * 获取预测历史
   */
  getPredictionHistory(modelId: string, limit: number = 100): PredictionResult[] {
    const history = this.predictionCache.get(modelId) || [];
    return history.slice(-limit);
  }

  /**
   * 删除模型
   */
  deleteModel(modelId: string): boolean {
    const deleted = this.models.delete(modelId);
    this.predictionCache.delete(modelId);
    
    if (deleted) {
      this.logger.log(`模型已删除: ${modelId}`);
    }
    
    return deleted;
  }

  /**
   * 定期重训练模型
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async retrainModels(): Promise<void> {
    try {
      this.logger.log('开始定期重训练模型');

      // 获取最近的数据
      const endDate = new Date();
      const startDate = moment(endDate).subtract(30, 'days').toDate();
      const recentData = await this.getTrainingData(startDate, endDate);

      if (recentData.length < 100) {
        this.logger.warn('训练数据不足，跳过重训练');
        return;
      }

      // 重训练每个模型
      for (const [modelId, model] of this.models) {
        try {
          const config: TrainingConfig = {
            modelType: model.type,
            algorithm: model.algorithm,
            features: model.features,
            target: model.target,
            trainingData: recentData,
            validationSplit: 0.2
          };

          const newModel = await this.trainPredictionModel(config);
          
          // 如果新模型性能更好，替换旧模型
          if (newModel.accuracy > model.accuracy) {
            this.models.set(modelId, {
              ...newModel,
              id: modelId,
              version: model.version + 1
            });
            this.logger.log(`模型已更新: ${modelId}, 新准确率: ${newModel.accuracy.toFixed(4)}`);
          }

        } catch (error) {
          this.logger.error(`重训练模型失败: ${modelId}`, error);
        }
      }

      this.logger.log('定期重训练完成');

    } catch (error) {
      this.logger.error('定期重训练失败', error);
    }
  }

  // 私有方法

  /**
   * 初始化模型
   */
  private initializeModels(): void {
    // 这里可以加载预训练的模型
    this.logger.log('机器学习服务初始化完成');
  }

  /**
   * 数据预处理
   */
  private preprocessData(data: any[], features: string[], target: string): any[] {
    return data.filter(item => {
      // 过滤掉缺失关键字段的数据
      const hasAllFeatures = features.every(feature => 
        item[feature] !== undefined && item[feature] !== null && !isNaN(item[feature])
      );
      const hasTarget = item[target] !== undefined && item[target] !== null && !isNaN(item[target]);
      
      return hasAllFeatures && hasTarget;
    });
  }

  /**
   * 训练线性回归模型
   */
  private trainLinearRegression(data: any[], features: string[], target: string): any {
    if (features.length === 1) {
      const x = data.map(d => d[features[0]]);
      const y = data.map(d => d[target]);
      return new SLR(x, y);
    } else {
      const x = data.map(d => features.map(f => d[f]));
      const y = data.map(d => d[target]);
      return new MLR(x, y);
    }
  }

  /**
   * 训练多项式回归模型
   */
  private trainPolynomialRegression(data: any[], features: string[], target: string, degree: number): any {
    if (features.length !== 1) {
      throw new Error('多项式回归目前只支持单特征');
    }
    
    const x = data.map(d => d[features[0]]);
    const y = data.map(d => d[target]);
    return new PolynomialRegression(x, y, degree);
  }

  /**
   * 训练多元回归模型
   */
  private trainMultipleRegression(data: any[], features: string[], target: string): any {
    const x = data.map(d => features.map(f => d[f]));
    const y = data.map(d => d[target]);
    return new MLR(x, y);
  }

  /**
   * 评估回归模型
   */
  private evaluateRegressionModel(model: any, validData: any[], features: string[], target: string): number {
    const predictions: number[] = [];
    const actuals: number[] = [];

    for (const data of validData) {
      const featureValues = this.extractFeatures(data, features);
      const prediction = model.predict(featureValues);
      const actual = data[target];

      predictions.push(prediction);
      actuals.push(actual);
    }

    // 计算R²分数
    const metrics = this.calculateRegressionMetrics(predictions, actuals);
    return metrics.r2;
  }

  /**
   * 提取特征
   */
  private extractFeatures(data: any, features: string[]): number[] | number {
    if (features.length === 1) {
      return data[features[0]];
    }
    return features.map(feature => data[feature]);
  }

  /**
   * 计算回归置信度
   */
  private calculateRegressionConfidence(model: MLModel, features: any): number {
    // 简化的置信度计算
    return Math.max(0, Math.min(1, model.accuracy));
  }

  /**
   * 创建特征映射
   */
  private createFeatureMap(featureNames: string[], featureValues: number[] | number): { [key: string]: number } {
    const map: { [key: string]: number } = {};
    
    if (Array.isArray(featureValues)) {
      featureNames.forEach((name, index) => {
        map[name] = featureValues[index];
      });
    } else {
      map[featureNames[0]] = featureValues;
    }
    
    return map;
  }

  /**
   * 缓存预测结果
   */
  private cachePrediction(modelId: string, result: PredictionResult): void {
    if (!this.predictionCache.has(modelId)) {
      this.predictionCache.set(modelId, []);
    }
    
    const cache = this.predictionCache.get(modelId)!;
    cache.push(result);
    
    // 限制缓存大小
    if (cache.length > 1000) {
      cache.shift();
    }
  }

  /**
   * 统计异常检测
   */
  private statisticalAnomalyDetection(data: any[], features: string[]): AnomalyResult[] {
    const results: AnomalyResult[] = [];

    for (const feature of features) {
      const values = data.map(d => d[feature]).filter(v => v !== undefined && v !== null);
      
      if (values.length < 2) continue;

      const mean = ss.mean(values);
      const stdDev = ss.standardDeviation(values);
      const threshold = mean + 2 * stdDev; // 2-sigma规则

      data.forEach(item => {
        const value = item[feature];
        if (value !== undefined && value !== null) {
          const zScore = Math.abs((value - mean) / stdDev);
          const isAnomaly = zScore > 2;
          
          if (isAnomaly) {
            results.push({
              timestamp: item.timestamp || new Date(),
              deviceId: item.deviceId || 'unknown',
              features: { [feature]: value },
              anomalyScore: zScore,
              isAnomaly,
              threshold,
              explanation: `${feature} 值 ${value.toFixed(2)} 超出正常范围 (Z-score: ${zScore.toFixed(2)})`
            });
          }
        }
      });
    }

    return results;
  }

  /**
   * 孤立森林异常检测（简化实现）
   */
  private isolationForestAnomalyDetection(data: any[], features: string[]): AnomalyResult[] {
    // 简化实现，实际应该使用专门的孤立森林算法
    return this.statisticalAnomalyDetection(data, features);
  }

  /**
   * 一类SVM异常检测（简化实现）
   */
  private oneClassSVMAnomalyDetection(data: any[], features: string[]): AnomalyResult[] {
    // 简化实现，实际应该使用SVM算法
    return this.statisticalAnomalyDetection(data, features);
  }

  /**
   * 计算回归指标
   */
  private calculateRegressionMetrics(predictions: number[], actuals: number[]): any {
    if (predictions.length !== actuals.length || predictions.length === 0) {
      return { mae: 0, mse: 0, rmse: 0, r2: 0 };
    }

    // MAE (Mean Absolute Error)
    const mae = ss.mean(predictions.map((pred, i) => Math.abs(pred - actuals[i])));

    // MSE (Mean Squared Error)
    const mse = ss.mean(predictions.map((pred, i) => Math.pow(pred - actuals[i], 2)));

    // RMSE (Root Mean Squared Error)
    const rmse = Math.sqrt(mse);

    // R² (Coefficient of Determination)
    const actualMean = ss.mean(actuals);
    const totalSumSquares = actuals.reduce((sum, actual) => sum + Math.pow(actual - actualMean, 2), 0);
    const residualSumSquares = predictions.reduce((sum, pred, i) => sum + Math.pow(actuals[i] - pred, 2), 0);
    const r2 = totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;

    return { mae, mse, rmse, r2 };
  }

  /**
   * 获取训练数据
   */
  private async getTrainingData(startDate: Date, endDate: Date): Promise<any[]> {
    // 模拟数据生成
    const data: any[] = [];
    const devices = ['device-001', 'device-002', 'device-003'];

    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      devices.forEach(deviceId => {
        data.push({
          timestamp: current.toDate(),
          deviceId,
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5,
          energyConsumption: 50 + Math.random() * 20
        });
      });
      current.add(1, 'hour');
    }

    return data;
  }
}
