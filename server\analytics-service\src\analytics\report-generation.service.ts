import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import * as fs from 'fs';
import * as path from 'path';
import moment from 'moment';
import * as ss from 'simple-statistics';
import { ProductionDataEntity } from '../entities/production-data.entity';
import { ReportEntity } from '../entities/report.entity';
import { AnalyticsService } from './analytics.service';
import { VisualizationService } from './visualization.service';

/**
 * 报表请求接口
 */
interface ReportRequest {
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  format: 'pdf' | 'excel' | 'html' | 'json';
  startDate: Date;
  endDate: Date;
  deviceIds?: string[];
  includeCharts?: boolean;
  includeRawData?: boolean;
  template?: string;
  customFields?: string[];
  filters?: any;
}

/**
 * 报表模板接口
 */
interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  sections: ReportSection[];
  styling?: any;
}

/**
 * 报表章节接口
 */
interface ReportSection {
  id: string;
  title: string;
  type: 'summary' | 'chart' | 'table' | 'kpi' | 'text' | 'image';
  content?: any;
  config?: any;
}

/**
 * 报表生成服务
 */
@Injectable()
export class ReportGenerationService {
  private readonly logger = new Logger(ReportGenerationService.name);

  // 报表模板
  private readonly templates: Map<string, ReportTemplate> = new Map();

  // 报表输出目录
  private readonly outputDir = path.join(process.cwd(), 'reports');

  constructor(
    @InjectRepository(ProductionDataEntity)
    private productionDataRepository: Repository<ProductionDataEntity>,
    @InjectRepository(ReportEntity)
    private reportRepository: Repository<ReportEntity>,
    private analyticsService: AnalyticsService,
    private visualizationService: VisualizationService
  ) {
    this.initializeOutputDirectory();
    this.initializeTemplates();
  }

  /**
   * 生成报表
   */
  async generateReport(request: ReportRequest, userId: string): Promise<ReportEntity> {
    try {
      this.logger.log(`开始生成报表: ${request.type} (${request.format})`);

      // 创建报表记录
      const report = this.reportRepository.create({
        reportType: request.type,
        title: this.generateReportTitle(request),
        description: this.generateReportDescription(request),
        startDate: request.startDate,
        endDate: request.endDate,
        format: request.format,
        status: 'generating',
        parameters: request,
        createdBy: userId
      });

      const savedReport = await this.reportRepository.save(report);

      // 异步生成报表内容
      this.generateReportContent(savedReport, request).catch(error => {
        this.logger.error(`报表生成失败: ${savedReport.id}`, error);
        this.updateReportStatus(savedReport.id, 'failed', { error: error.message });
      });

      return savedReport;

    } catch (error) {
      this.logger.error('创建报表记录失败', error);
      throw error;
    }
  }

  /**
   * 生成定期报表
   */
  @Cron(CronExpression.EVERY_DAY_AT_6AM)
  async generateDailyReports(): Promise<void> {
    try {
      const yesterday = moment().subtract(1, 'day');
      const request: ReportRequest = {
        type: 'daily',
        format: 'pdf',
        startDate: yesterday.startOf('day').toDate(),
        endDate: yesterday.endOf('day').toDate(),
        includeCharts: true,
        includeRawData: false
      };

      await this.generateReport(request, 'system');
      this.logger.log('每日报表生成完成');

    } catch (error) {
      this.logger.error('生成每日报表失败', error);
    }
  }

  /**
   * 生成周报表
   */
  @Cron(CronExpression.EVERY_MONDAY_AT_7AM)
  async generateWeeklyReports(): Promise<void> {
    try {
      const lastWeek = moment().subtract(1, 'week');
      const request: ReportRequest = {
        type: 'weekly',
        format: 'excel',
        startDate: lastWeek.startOf('week').toDate(),
        endDate: lastWeek.endOf('week').toDate(),
        includeCharts: true,
        includeRawData: true
      };

      await this.generateReport(request, 'system');
      this.logger.log('周报表生成完成');

    } catch (error) {
      this.logger.error('生成周报表失败', error);
    }
  }

  /**
   * 生成月报表
   */
  @Cron('0 8 1 * *') // 每月1号8点
  async generateMonthlyReports(): Promise<void> {
    try {
      const lastMonth = moment().subtract(1, 'month');
      const request: ReportRequest = {
        type: 'monthly',
        format: 'pdf',
        startDate: lastMonth.startOf('month').toDate(),
        endDate: lastMonth.endOf('month').toDate(),
        includeCharts: true,
        includeRawData: false
      };

      await this.generateReport(request, 'system');
      this.logger.log('月报表生成完成');

    } catch (error) {
      this.logger.error('生成月报表失败', error);
    }
  }

  /**
   * 获取报表列表
   */
  async getReports(
    page: number = 1,
    limit: number = 20,
    filters?: any
  ): Promise<{ reports: ReportEntity[]; total: number }> {
    try {
      const queryBuilder = this.reportRepository.createQueryBuilder('report');

      // 应用过滤器
      if (filters?.type) {
        queryBuilder.andWhere('report.reportType = :type', { type: filters.type });
      }
      if (filters?.status) {
        queryBuilder.andWhere('report.status = :status', { status: filters.status });
      }
      if (filters?.startDate) {
        queryBuilder.andWhere('report.createdAt >= :startDate', { startDate: filters.startDate });
      }
      if (filters?.endDate) {
        queryBuilder.andWhere('report.createdAt <= :endDate', { endDate: filters.endDate });
      }

      // 分页
      queryBuilder
        .orderBy('report.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [reports, total] = await queryBuilder.getManyAndCount();

      return { reports, total };

    } catch (error) {
      this.logger.error('获取报表列表失败', error);
      throw error;
    }
  }

  /**
   * 下载报表
   */
  async downloadReport(reportId: string): Promise<{ filePath: string; fileName: string }> {
    try {
      const report = await this.reportRepository.findOne({ where: { id: reportId } });

      if (!report) {
        throw new Error('报表不存在');
      }

      if (report.status !== 'completed') {
        throw new Error('报表尚未生成完成');
      }

      if (!report.filePath || !fs.existsSync(report.filePath)) {
        throw new Error('报表文件不存在');
      }

      // 更新下载次数
      await this.reportRepository.update(reportId, {
        downloadCount: report.downloadCount + 1
      });

      const fileName = path.basename(report.filePath);
      return { filePath: report.filePath, fileName };

    } catch (error) {
      this.logger.error('下载报表失败', error);
      throw error;
    }
  }

  /**
   * 删除报表
   */
  async deleteReport(reportId: string): Promise<boolean> {
    try {
      const report = await this.reportRepository.findOne({ where: { id: reportId } });

      if (!report) {
        return false;
      }

      // 删除文件
      if (report.filePath && fs.existsSync(report.filePath)) {
        fs.unlinkSync(report.filePath);
      }

      // 删除数据库记录
      await this.reportRepository.delete(reportId);

      this.logger.log(`报表已删除: ${reportId}`);
      return true;

    } catch (error) {
      this.logger.error('删除报表失败', error);
      throw error;
    }
  }

  /**
   * 获取报表模板
   */
  getReportTemplates(): ReportTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 创建自定义报表模板
   */
  async createReportTemplate(template: ReportTemplate): Promise<ReportTemplate> {
    try {
      this.templates.set(template.id, template);
      this.logger.log(`报表模板已创建: ${template.name}`);
      return template;

    } catch (error) {
      this.logger.error('创建报表模板失败', error);
      throw error;
    }
  }

  // 私有方法

  /**
   * 初始化输出目录
   */
  private initializeOutputDirectory(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
    this.logger.log(`报表输出目录: ${this.outputDir}`);
  }

  /**
   * 初始化报表模板
   */
  private initializeTemplates(): void {
    // 日报模板
    this.templates.set('daily-standard', {
      id: 'daily-standard',
      name: '标准日报',
      description: '包含KPI、趋势分析和异常报告的标准日报模板',
      type: 'daily',
      sections: [
        { id: 'summary', title: '执行摘要', type: 'summary' },
        { id: 'kpi', title: 'KPI指标', type: 'kpi' },
        { id: 'trends', title: '趋势分析', type: 'chart' },
        { id: 'issues', title: '问题分析', type: 'table' },
        { id: 'recommendations', title: '改进建议', type: 'text' }
      ]
    });

    // 周报模板
    this.templates.set('weekly-comprehensive', {
      id: 'weekly-comprehensive',
      name: '综合周报',
      description: '包含详细分析和对比的综合周报模板',
      type: 'weekly',
      sections: [
        { id: 'executive-summary', title: '管理层摘要', type: 'summary' },
        { id: 'performance-overview', title: '性能概览', type: 'kpi' },
        { id: 'trend-analysis', title: '趋势分析', type: 'chart' },
        { id: 'device-comparison', title: '设备对比', type: 'chart' },
        { id: 'quality-analysis', title: '质量分析', type: 'chart' },
        { id: 'energy-efficiency', title: '能效分析', type: 'chart' },
        { id: 'maintenance-schedule', title: '维护计划', type: 'table' },
        { id: 'action-items', title: '行动项目', type: 'text' }
      ]
    });

    // 月报模板
    this.templates.set('monthly-executive', {
      id: 'monthly-executive',
      name: '管理层月报',
      description: '面向管理层的高级月度报告模板',
      type: 'monthly',
      sections: [
        { id: 'key-metrics', title: '关键指标', type: 'kpi' },
        { id: 'monthly-trends', title: '月度趋势', type: 'chart' },
        { id: 'cost-analysis', title: '成本分析', type: 'chart' },
        { id: 'roi-analysis', title: 'ROI分析', type: 'chart' },
        { id: 'strategic-recommendations', title: '战略建议', type: 'text' }
      ]
    });

    this.logger.log('报表模板初始化完成');
  }

  /**
   * 生成报表内容
   */
  private async generateReportContent(report: ReportEntity, request: ReportRequest): Promise<void> {
    try {
      // 收集数据
      const reportData = await this.collectReportData(request);

      // 选择模板
      const template = this.selectTemplate(request);

      // 生成内容
      const content = await this.buildReportContent(reportData, template, request);

      // 生成文件
      const filePath = await this.generateReportFile(content, request, report.id);

      // 更新报表状态
      await this.updateReportStatus(report.id, 'completed', {
        data: content,
        filePath,
        fileSize: fs.statSync(filePath).size,
        completedAt: new Date()
      });

      this.logger.log(`报表生成完成: ${report.id}`);

    } catch (error) {
      await this.updateReportStatus(report.id, 'failed', { error: error.message });
      throw error;
    }
  }

  /**
   * 收集报表数据
   */
  private async collectReportData(request: ReportRequest): Promise<any> {
    const data: any = {};

    // 获取生产数据
    data.productionData = await this.getProductionData(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    // 计算KPI指标
    data.kpiMetrics = await this.analyticsService.calculateKPIMetrics(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    // 趋势分析
    data.trends = await this.analyticsService.performTrendAnalysis(
      'efficiency',
      moment(request.endDate).diff(moment(request.startDate), 'days'),
      request.deviceIds
    );

    // 异常检测
    data.anomalies = await this.analyticsService.detectAnomalies(
      request.deviceIds,
      0.8
    );

    // 生成图表（如果需要）
    if (request.includeCharts) {
      data.charts = await this.generateReportCharts(request);
    }

    return data;
  }

  /**
   * 选择报表模板
   */
  private selectTemplate(request: ReportRequest): ReportTemplate {
    const templateId = request.template || `${request.type}-standard`;
    return this.templates.get(templateId) || this.templates.get('daily-standard')!;
  }

  /**
   * 构建报表内容
   */
  private async buildReportContent(
    data: any,
    template: ReportTemplate,
    request: ReportRequest
  ): Promise<any> {
    const content: any = {
      title: this.generateReportTitle(request),
      subtitle: this.generateReportDescription(request),
      period: {
        startDate: request.startDate,
        endDate: request.endDate,
        duration: moment(request.endDate).diff(moment(request.startDate), 'days') + 1
      },
      generatedAt: new Date(),
      sections: []
    };

    // 处理每个章节
    for (const section of template.sections) {
      const sectionContent = await this.buildSectionContent(section, data, request);
      content.sections.push(sectionContent);
    }

    return content;
  }

  /**
   * 构建章节内容
   */
  private async buildSectionContent(
    section: ReportSection,
    data: any,
    request: ReportRequest
  ): Promise<any> {
    const sectionContent: any = {
      id: section.id,
      title: section.title,
      type: section.type,
      content: null
    };

    switch (section.type) {
      case 'summary':
        sectionContent.content = this.buildSummaryContent(data);
        break;
      case 'kpi':
        sectionContent.content = this.buildKPIContent(data.kpiMetrics);
        break;
      case 'chart':
        sectionContent.content = await this.buildChartContent(section, data);
        break;
      case 'table':
        sectionContent.content = this.buildTableContent(section, data);
        break;
      case 'text':
        sectionContent.content = this.buildTextContent(section, data);
        break;
    }

    return sectionContent;
  }

  /**
   * 生成报表文件
   */
  private async generateReportFile(
    content: any,
    request: ReportRequest,
    reportId: string
  ): Promise<string> {
    const fileName = `report_${reportId}_${moment().format('YYYYMMDD_HHmmss')}.${request.format}`;
    const filePath = path.join(this.outputDir, fileName);

    switch (request.format) {
      case 'pdf':
        await this.generatePDFReport(content, filePath);
        break;
      case 'excel':
        await this.generateExcelReport(content, filePath);
        break;
      case 'html':
        await this.generateHTMLReport(content, filePath);
        break;
      case 'json':
        await this.generateJSONReport(content, filePath);
        break;
      default:
        throw new Error(`不支持的报表格式: ${request.format}`);
    }

    return filePath;
  }

  /**
   * 生成PDF报表
   */
  private async generatePDFReport(content: any, filePath: string): Promise<void> {
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // 标题
    doc.fontSize(20).text(content.title, { align: 'center' });
    doc.fontSize(14).text(content.subtitle, { align: 'center' });
    doc.moveDown();

    // 报表期间
    doc.fontSize(12).text(`报表期间: ${moment(content.period.startDate).format('YYYY-MM-DD')} 至 ${moment(content.period.endDate).format('YYYY-MM-DD')}`);
    doc.text(`生成时间: ${moment(content.generatedAt).format('YYYY-MM-DD HH:mm:ss')}`);
    doc.moveDown();

    // 章节内容
    for (const section of content.sections) {
      doc.fontSize(16).text(section.title);
      doc.moveDown(0.5);

      if (section.type === 'kpi' && section.content) {
        this.addKPIToPDF(doc, section.content);
      } else if (section.type === 'table' && section.content) {
        this.addTableToPDF(doc, section.content);
      } else if (section.content) {
        doc.fontSize(10).text(JSON.stringify(section.content, null, 2));
      }

      doc.moveDown();
    }

    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * 生成Excel报表
   */
  private async generateExcelReport(content: any, filePath: string): Promise<void> {
    const workbook = new ExcelJS.Workbook();

    // 摘要工作表
    const summarySheet = workbook.addWorksheet('摘要');
    summarySheet.addRow(['报表标题', content.title]);
    summarySheet.addRow(['报表描述', content.subtitle]);
    summarySheet.addRow(['开始日期', moment(content.period.startDate).format('YYYY-MM-DD')]);
    summarySheet.addRow(['结束日期', moment(content.period.endDate).format('YYYY-MM-DD')]);
    summarySheet.addRow(['生成时间', moment(content.generatedAt).format('YYYY-MM-DD HH:mm:ss')]);

    // KPI工作表
    const kpiSection = content.sections.find((s: any) => s.type === 'kpi');
    if (kpiSection && kpiSection.content) {
      const kpiSheet = workbook.addWorksheet('KPI指标');
      this.addKPIToExcel(kpiSheet, kpiSection.content);
    }

    // 数据工作表
    const dataSection = content.sections.find((s: any) => s.type === 'table');
    if (dataSection && dataSection.content) {
      const dataSheet = workbook.addWorksheet('详细数据');
      this.addTableToExcel(dataSheet, dataSection.content);
    }

    await workbook.xlsx.writeFile(filePath);
  }

  /**
   * 生成HTML报表
   */
  private async generateHTMLReport(content: any, filePath: string): Promise<void> {
    let html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>${content.title}</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 30px; }
            .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .kpi-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>${content.title}</h1>
            <h3>${content.subtitle}</h3>
            <p>报表期间: ${moment(content.period.startDate).format('YYYY-MM-DD')} 至 ${moment(content.period.endDate).format('YYYY-MM-DD')}</p>
            <p>生成时间: ${moment(content.generatedAt).format('YYYY-MM-DD HH:mm:ss')}</p>
        </div>
    `;

    // 添加章节
    for (const section of content.sections) {
      html += `<div class="section">`;
      html += `<h2>${section.title}</h2>`;

      if (section.type === 'kpi' && section.content) {
        html += this.buildKPIHTML(section.content);
      } else if (section.type === 'table' && section.content) {
        html += this.buildTableHTML(section.content);
      } else if (section.content) {
        html += `<pre>${JSON.stringify(section.content, null, 2)}</pre>`;
      }

      html += `</div>`;
    }

    html += `</body></html>`;

    fs.writeFileSync(filePath, html, 'utf8');
  }

  /**
   * 生成JSON报表
   */
  private async generateJSONReport(content: any, filePath: string): Promise<void> {
    fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
  }

  /**
   * 生成报表标题
   */
  private generateReportTitle(request: ReportRequest): string {
    const typeNames = {
      daily: '日报',
      weekly: '周报',
      monthly: '月报',
      quarterly: '季报',
      yearly: '年报',
      custom: '自定义报表'
    };

    const typeName = typeNames[request.type] || '报表';
    const dateStr = moment(request.startDate).format('YYYY-MM-DD');

    return `生产分析${typeName} - ${dateStr}`;
  }

  /**
   * 生成报表描述
   */
  private generateReportDescription(request: ReportRequest): string {
    const startDate = moment(request.startDate).format('YYYY年MM月DD日');
    const endDate = moment(request.endDate).format('YYYY年MM月DD日');

    return `${startDate} 至 ${endDate} 生产数据分析报告`;
  }

  /**
   * 更新报表状态
   */
  private async updateReportStatus(
    reportId: string,
    status: 'pending' | 'generating' | 'completed' | 'failed',
    updates: any = {}
  ): Promise<void> {
    await this.reportRepository.update(reportId, {
      status,
      ...updates
    });
  }

  /**
   * 获取生产数据
   */
  private async getProductionData(startDate: Date, endDate: Date, deviceIds?: string[]): Promise<any[]> {
    // 模拟数据生成
    const data: any[] = [];
    const devices = deviceIds || ['device-001', 'device-002', 'device-003'];

    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      devices.forEach(deviceId => {
        data.push({
          timestamp: current.toDate(),
          deviceId,
          deviceType: 'CNC_MACHINE',
          productionLine: 'LINE_001',
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          downtime: Math.random() * 30,
          energyConsumption: 50 + Math.random() * 20,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5
        });
      });
      current.add(1, 'hour');
    }

    return data;
  }

  /**
   * 生成报表图表
   */
  private async generateReportCharts(request: ReportRequest): Promise<any> {
    const charts: any = {};

    // 生产效率图表
    charts.efficiency = await this.visualizationService.createProductionEfficiencyChart(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    // 质量分析图表
    charts.quality = await this.visualizationService.createQualityAnalysisChart(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    // 能耗分析图表
    charts.energy = await this.visualizationService.createEnergyConsumptionChart(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    // OEE仪表盘
    charts.oee = await this.visualizationService.createOEEGaugeChart(
      request.startDate,
      request.endDate,
      request.deviceIds
    );

    return charts;
  }

  /**
   * 构建摘要内容
   */
  private buildSummaryContent(data: any): any {
    return {
      totalOutput: data.productionData?.reduce((sum: number, d: any) => sum + d.output, 0) || 0,
      averageQuality: data.productionData?.length > 0 ?
        ss.mean(data.productionData.map((d: any) => d.quality)) : 0,
      totalDowntime: data.productionData?.reduce((sum: number, d: any) => sum + d.downtime, 0) || 0,
      energyConsumption: data.productionData?.reduce((sum: number, d: any) => sum + d.energyConsumption, 0) || 0,
      oeeScore: data.kpiMetrics?.oee || 0,
      anomalyCount: data.anomalies?.length || 0,
      trendDirection: data.trends?.trend || 'stable'
    };
  }

  /**
   * 构建KPI内容
   */
  private buildKPIContent(kpiMetrics: any): any {
    if (!kpiMetrics) return null;

    return {
      oee: { value: kpiMetrics.oee, unit: '%', label: 'OEE' },
      availability: { value: kpiMetrics.availability, unit: '%', label: '可用性' },
      performance: { value: kpiMetrics.performance, unit: '%', label: '性能' },
      quality: { value: kpiMetrics.quality, unit: '%', label: '质量' },
      throughput: { value: kpiMetrics.throughput, unit: '件', label: '产量' },
      energyEfficiency: { value: kpiMetrics.energyEfficiency, unit: '件/kWh', label: '能效' }
    };
  }

  /**
   * 构建图表内容
   */
  private async buildChartContent(section: ReportSection, data: any): Promise<any> {
    if (!data.charts) return null;

    switch (section.id) {
      case 'trends':
        return data.charts.efficiency;
      case 'device-comparison':
        return data.charts.energy;
      case 'quality-analysis':
        return data.charts.quality;
      default:
        return data.charts.efficiency;
    }
  }

  /**
   * 构建表格内容
   */
  private buildTableContent(section: ReportSection, data: any): any {
    switch (section.id) {
      case 'issues':
        return this.buildIssuesTable(data.anomalies);
      case 'maintenance-schedule':
        return this.buildMaintenanceTable();
      default:
        return this.buildDataTable(data.productionData);
    }
  }

  /**
   * 构建文本内容
   */
  private buildTextContent(section: ReportSection, data: any): any {
    switch (section.id) {
      case 'recommendations':
        return this.buildRecommendations(data);
      case 'action-items':
        return this.buildActionItems(data);
      case 'strategic-recommendations':
        return this.buildStrategicRecommendations(data);
      default:
        return { text: '暂无内容' };
    }
  }

  /**
   * 构建问题表格
   */
  private buildIssuesTable(anomalies: any[]): any {
    if (!anomalies || anomalies.length === 0) {
      return {
        headers: ['设备ID', '问题类型', '严重程度', '时间'],
        rows: [['无', '无异常', '正常', '正常']]
      };
    }

    return {
      headers: ['设备ID', '指标', '异常值', '严重程度', '时间'],
      rows: anomalies.map(anomaly => [
        anomaly.deviceId,
        anomaly.metric,
        anomaly.value?.toFixed(2) || 'N/A',
        anomaly.severity,
        moment(anomaly.timestamp).format('MM-DD HH:mm')
      ])
    };
  }

  /**
   * 构建维护表格
   */
  private buildMaintenanceTable(): any {
    return {
      headers: ['设备ID', '维护类型', '计划时间', '状态'],
      rows: [
        ['device-001', '定期保养', '2024-01-15', '已完成'],
        ['device-002', '零件更换', '2024-01-20', '计划中'],
        ['device-003', '校准检查', '2024-01-25', '计划中']
      ]
    };
  }

  /**
   * 构建数据表格
   */
  private buildDataTable(productionData: any[]): any {
    if (!productionData || productionData.length === 0) {
      return {
        headers: ['时间', '设备ID', '产量', '质量', '效率'],
        rows: [['无数据', '', '', '', '']]
      };
    }

    return {
      headers: ['时间', '设备ID', '产量', '质量(%)', '效率(%)'],
      rows: productionData.slice(0, 20).map(data => [
        moment(data.timestamp).format('MM-DD HH:mm'),
        data.deviceId,
        data.output?.toFixed(1) || '0',
        data.quality?.toFixed(1) || '0',
        data.efficiency?.toFixed(1) || '0'
      ])
    };
  }

  /**
   * 构建建议内容
   */
  private buildRecommendations(data: any): any {
    const recommendations = [];

    if (data.kpiMetrics?.oee < 70) {
      recommendations.push('OEE偏低，建议检查设备维护计划和操作流程');
    }

    if (data.kpiMetrics?.quality < 95) {
      recommendations.push('质量指标需要改善，建议加强质量控制和员工培训');
    }

    if (data.kpiMetrics?.energyEfficiency < 2) {
      recommendations.push('能耗效率偏低，建议优化设备运行参数和节能措施');
    }

    if (data.anomalies?.length > 5) {
      recommendations.push('异常事件较多，建议加强预防性维护和监控');
    }

    if (recommendations.length === 0) {
      recommendations.push('系统运行良好，继续保持当前操作标准');
    }

    return { recommendations };
  }

  /**
   * 构建行动项目
   */
  private buildActionItems(data: any): any {
    const actionItems = [
      '检查设备运行状态，确保正常运行',
      '更新维护计划，预防设备故障',
      '培训操作人员，提高操作技能',
      '优化生产流程，提高效率'
    ];

    return { actionItems };
  }

  /**
   * 构建战略建议
   */
  private buildStrategicRecommendations(data: any): any {
    const recommendations = [
      '投资自动化设备，提高生产效率',
      '建立数据驱动的决策体系',
      '加强供应链管理，降低成本',
      '推进数字化转型，提升竞争力'
    ];

    return { recommendations };
  }

  /**
   * 添加KPI到PDF
   */
  private addKPIToPDF(doc: any, kpiContent: any): void {
    Object.entries(kpiContent).forEach(([key, kpi]: [string, any]) => {
      doc.fontSize(10).text(`${kpi.label}: ${kpi.value.toFixed(2)}${kpi.unit}`);
    });
  }

  /**
   * 添加表格到PDF
   */
  private addTableToPDF(doc: any, tableContent: any): void {
    // 简化的表格实现
    doc.fontSize(10);
    doc.text(tableContent.headers.join(' | '));
    tableContent.rows.forEach((row: any[]) => {
      doc.text(row.join(' | '));
    });
  }

  /**
   * 添加KPI到Excel
   */
  private addKPIToExcel(sheet: any, kpiContent: any): void {
    sheet.addRow(['指标', '数值', '单位']);
    Object.entries(kpiContent).forEach(([key, kpi]: [string, any]) => {
      sheet.addRow([kpi.label, kpi.value, kpi.unit]);
    });
  }

  /**
   * 添加表格到Excel
   */
  private addTableToExcel(sheet: any, tableContent: any): void {
    sheet.addRow(tableContent.headers);
    tableContent.rows.forEach((row: any[]) => {
      sheet.addRow(row);
    });
  }

  /**
   * 构建KPI HTML
   */
  private buildKPIHTML(kpiContent: any): string {
    let html = '<div class="kpi-grid">';
    Object.entries(kpiContent).forEach(([key, kpi]: [string, any]) => {
      html += `
        <div class="kpi-card">
          <h4>${kpi.label}</h4>
          <p>${kpi.value.toFixed(2)}${kpi.unit}</p>
        </div>
      `;
    });
    html += '</div>';
    return html;
  }

  /**
   * 构建表格HTML
   */
  private buildTableHTML(tableContent: any): string {
    let html = '<table>';
    html += '<tr>';
    tableContent.headers.forEach((header: string) => {
      html += `<th>${header}</th>`;
    });
    html += '</tr>';

    tableContent.rows.forEach((row: any[]) => {
      html += '<tr>';
      row.forEach((cell: any) => {
        html += `<td>${cell}</td>`;
      });
      html += '</tr>';
    });

    html += '</table>';
    return html;
  }
}