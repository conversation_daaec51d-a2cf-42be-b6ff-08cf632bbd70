import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import moment from 'moment';
import * as crypto from 'crypto';
import { SyncTaskEntity } from '../entities/sync-task.entity';
import { SyncErrorEntity } from '../entities/sync-error.entity';
import { EnterpriseSystemEntity } from '../entities/enterprise-system.entity';

/**
 * 同步策略接口
 */
interface SyncStrategy {
  type: 'full' | 'incremental' | 'delta' | 'real_time';
  batchSize: number;
  conflictResolution: 'source_wins' | 'target_wins' | 'merge' | 'manual';
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

/**
 * 数据映射配置接口
 */
interface DataMapping {
  sourceField: string;
  targetField: string;
  transformation?: {
    type: 'format' | 'lookup' | 'calculation' | 'custom';
    config: any;
  };
  validation?: {
    required: boolean;
    type: string;
    constraints?: any;
  };
}

/**
 * 同步规则接口
 */
interface SyncRule {
  id: string;
  name: string;
  sourceSystem: string;
  targetSystem: string;
  entityType: string;
  strategy: SyncStrategy;
  mappings: DataMapping[];
  filters?: any;
  schedule?: string;
  enabled: boolean;
}

/**
 * 冲突解决接口
 */
interface ConflictResolution {
  recordId: string;
  sourceData: any;
  targetData: any;
  conflictFields: string[];
  resolution: 'source' | 'target' | 'merge' | 'skip';
  resolvedData?: any;
  resolvedBy?: string;
  resolvedAt?: Date;
}

/**
 * 数据同步服务
 */
@Injectable()
export class DataSyncService {
  private readonly logger = new Logger(DataSyncService.name);
  
  // 同步规则缓存
  private readonly syncRules: Map<string, SyncRule> = new Map();
  
  // 正在运行的同步任务
  private readonly runningSyncs: Set<string> = new Set();
  
  // 冲突队列
  private readonly conflictQueue: Map<string, ConflictResolution[]> = new Map();

  constructor(
    @InjectRepository(SyncTaskEntity)
    private syncTaskRepository: Repository<SyncTaskEntity>,
    @InjectRepository(SyncErrorEntity)
    private syncErrorRepository: Repository<SyncErrorEntity>,
    @InjectRepository(EnterpriseSystemEntity)
    private enterpriseSystemRepository: Repository<EnterpriseSystemEntity>,
    private eventEmitter: EventEmitter2
  ) {
    this.initializeSyncRules();
  }

  /**
   * 创建同步规则
   */
  async createSyncRule(rule: SyncRule): Promise<SyncRule> {
    try {
      this.logger.log(`创建同步规则: ${rule.name}`);

      // 验证系统存在
      await this.validateSystems(rule.sourceSystem, rule.targetSystem);

      // 验证映射配置
      this.validateMappings(rule.mappings);

      // 存储规则
      this.syncRules.set(rule.id, rule);

      this.logger.log(`同步规则创建完成: ${rule.name}`);
      return rule;

    } catch (error) {
      this.logger.error('创建同步规则失败', error);
      throw error;
    }
  }

  /**
   * 执行数据同步
   */
  async executeSync(ruleId: string, options?: any): Promise<any> {
    try {
      const rule = this.syncRules.get(ruleId);
      if (!rule) {
        throw new Error(`同步规则不存在: ${ruleId}`);
      }

      if (!rule.enabled) {
        throw new Error(`同步规则已禁用: ${ruleId}`);
      }

      if (this.runningSyncs.has(ruleId)) {
        throw new Error(`同步任务正在运行: ${ruleId}`);
      }

      this.logger.log(`开始执行同步: ${rule.name}`);
      this.runningSyncs.add(ruleId);

      // 创建同步任务记录
      const syncTask = await this.createSyncTask(rule, options);

      try {
        let result;
        switch (rule.strategy.type) {
          case 'full':
            result = await this.executeFullSync(rule, syncTask.id);
            break;
          case 'incremental':
            result = await this.executeIncrementalSync(rule, syncTask.id);
            break;
          case 'delta':
            result = await this.executeDeltaSync(rule, syncTask.id);
            break;
          case 'real_time':
            result = await this.executeRealtimeSync(rule, syncTask.id);
            break;
          default:
            throw new Error(`不支持的同步策略: ${rule.strategy.type}`);
        }

        // 完成同步任务
        await this.completeSyncTask(syncTask.id, result);

        // 发送同步完成事件
        this.eventEmitter.emit('sync.completed', {
          ruleId,
          taskId: syncTask.id,
          result
        });

        this.logger.log(`同步执行完成: ${rule.name}, 处理记录: ${result.processedCount}`);
        return result;

      } catch (error) {
        await this.failSyncTask(syncTask.id, error.message);
        
        // 发送同步失败事件
        this.eventEmitter.emit('sync.failed', {
          ruleId,
          taskId: syncTask.id,
          error: error.message
        });

        throw error;
      } finally {
        this.runningSyncs.delete(ruleId);
      }

    } catch (error) {
      this.logger.error('执行数据同步失败', error);
      throw error;
    }
  }

  /**
   * 批量数据同步
   */
  async batchSync(ruleIds: string[]): Promise<any[]> {
    const results: any[] = [];

    for (const ruleId of ruleIds) {
      try {
        const result = await this.executeSync(ruleId);
        results.push({ ruleId, success: true, result });
      } catch (error) {
        results.push({ ruleId, success: false, error: error.message });
      }
    }

    return results;
  }

  /**
   * 实时数据推送
   */
  async pushRealtimeData(
    sourceSystem: string,
    targetSystem: string,
    entityType: string,
    data: any
  ): Promise<boolean> {
    try {
      // 查找匹配的同步规则
      const rule = this.findSyncRule(sourceSystem, targetSystem, entityType);
      if (!rule || !rule.enabled) {
        this.logger.warn(`未找到启用的同步规则: ${sourceSystem} -> ${targetSystem} (${entityType})`);
        return false;
      }

      // 数据转换
      const transformedData = await this.transformData(data, rule.mappings);

      // 验证数据
      const validationResult = this.validateData(transformedData, rule.mappings);
      if (!validationResult.valid) {
        throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
      }

      // 推送数据到目标系统
      const success = await this.pushToTargetSystem(targetSystem, entityType, transformedData);

      if (success) {
        // 记录成功的推送
        this.eventEmitter.emit('realtime.push.success', {
          sourceSystem,
          targetSystem,
          entityType,
          data: transformedData
        });
      }

      return success;

    } catch (error) {
      this.logger.error('实时数据推送失败', error);
      
      // 记录推送错误
      this.eventEmitter.emit('realtime.push.error', {
        sourceSystem,
        targetSystem,
        entityType,
        error: error.message
      });

      return false;
    }
  }

  /**
   * 处理数据冲突
   */
  async resolveConflict(
    conflictId: string,
    resolution: 'source' | 'target' | 'merge' | 'skip',
    mergedData?: any,
    resolvedBy?: string
  ): Promise<boolean> {
    try {
      // 查找冲突
      let conflict: ConflictResolution | undefined;
      let queueKey: string | undefined;

      for (const [key, conflicts] of this.conflictQueue) {
        const found = conflicts.find(c => c.recordId === conflictId);
        if (found) {
          conflict = found;
          queueKey = key;
          break;
        }
      }

      if (!conflict || !queueKey) {
        throw new Error(`冲突记录不存在: ${conflictId}`);
      }

      // 设置解决方案
      conflict.resolution = resolution;
      conflict.resolvedBy = resolvedBy;
      conflict.resolvedAt = new Date();

      if (resolution === 'merge' && mergedData) {
        conflict.resolvedData = mergedData;
      } else if (resolution === 'source') {
        conflict.resolvedData = conflict.sourceData;
      } else if (resolution === 'target') {
        conflict.resolvedData = conflict.targetData;
      }

      // 如果不是跳过，则应用解决方案
      if (resolution !== 'skip' && conflict.resolvedData) {
        await this.applyConflictResolution(conflict);
      }

      // 从队列中移除已解决的冲突
      const conflicts = this.conflictQueue.get(queueKey)!;
      const index = conflicts.findIndex(c => c.recordId === conflictId);
      if (index >= 0) {
        conflicts.splice(index, 1);
      }

      // 发送冲突解决事件
      this.eventEmitter.emit('conflict.resolved', {
        conflictId,
        resolution,
        resolvedBy
      });

      this.logger.log(`数据冲突已解决: ${conflictId} (${resolution})`);
      return true;

    } catch (error) {
      this.logger.error('处理数据冲突失败', error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  async getSyncStatus(ruleId?: string): Promise<any> {
    try {
      if (ruleId) {
        // 获取特定规则的状态
        const rule = this.syncRules.get(ruleId);
        if (!rule) {
          throw new Error(`同步规则不存在: ${ruleId}`);
        }

        const recentTasks = await this.syncTaskRepository.find({
          where: { syncRuleId: ruleId },
          order: { createdAt: 'DESC' },
          take: 10
        });

        const errorCount = await this.syncErrorRepository.count({
          where: { 
            syncRuleId: ruleId,
            createdAt: moment().subtract(24, 'hours').toDate()
          }
        });

        return {
          ruleId,
          ruleName: rule.name,
          enabled: rule.enabled,
          isRunning: this.runningSyncs.has(ruleId),
          lastSyncAt: recentTasks.length > 0 ? recentTasks[0].createdAt : null,
          recentTasks: recentTasks.map(task => ({
            id: task.id,
            status: task.status,
            processedCount: task.processedCount,
            errorCount: task.errorCount,
            createdAt: task.createdAt
          })),
          errorCount24h: errorCount
        };

      } else {
        // 获取所有规则的状态
        const allRules = Array.from(this.syncRules.values());
        const statuses = await Promise.all(
          allRules.map(rule => this.getSyncStatus(rule.id))
        );

        return {
          totalRules: allRules.length,
          enabledRules: allRules.filter(r => r.enabled).length,
          runningTasks: this.runningSyncs.size,
          rules: statuses
        };
      }

    } catch (error) {
      this.logger.error('获取同步状态失败', error);
      throw error;
    }
  }

  /**
   * 获取冲突列表
   */
  getConflicts(systemPair?: string): ConflictResolution[] {
    if (systemPair) {
      return this.conflictQueue.get(systemPair) || [];
    }

    const allConflicts: ConflictResolution[] = [];
    this.conflictQueue.forEach(conflicts => {
      allConflicts.push(...conflicts);
    });

    return allConflicts;
  }

  /**
   * 定期同步任务
   */
  @Cron(CronExpression.EVERY_30_MINUTES)
  async scheduledSync(): Promise<void> {
    try {
      this.logger.log('开始定期同步检查');

      const enabledRules = Array.from(this.syncRules.values())
        .filter(rule => rule.enabled && rule.schedule);

      for (const rule of enabledRules) {
        try {
          if (this.shouldRunScheduledSync(rule)) {
            await this.executeSync(rule.id);
          }
        } catch (error) {
          this.logger.error(`定期同步失败: ${rule.name}`, error);
        }
      }

      this.logger.log('定期同步检查完成');

    } catch (error) {
      this.logger.error('定期同步检查失败', error);
    }
  }

  // 私有方法

  /**
   * 初始化同步规则
   */
  private async initializeSyncRules(): Promise<void> {
    // 这里可以从数据库或配置文件加载同步规则
    this.logger.log('同步规则初始化完成');
  }

  /**
   * 验证系统存在
   */
  private async validateSystems(sourceSystem: string, targetSystem: string): Promise<void> {
    const source = await this.enterpriseSystemRepository.findOne({
      where: { systemId: sourceSystem }
    });

    const target = await this.enterpriseSystemRepository.findOne({
      where: { systemId: targetSystem }
    });

    if (!source) {
      throw new Error(`源系统不存在: ${sourceSystem}`);
    }

    if (!target) {
      throw new Error(`目标系统不存在: ${targetSystem}`);
    }
  }

  /**
   * 验证映射配置
   */
  private validateMappings(mappings: DataMapping[]): void {
    if (!mappings || mappings.length === 0) {
      throw new Error('映射配置不能为空');
    }

    for (const mapping of mappings) {
      if (!mapping.sourceField || !mapping.targetField) {
        throw new Error('映射配置必须包含源字段和目标字段');
      }
    }
  }

  /**
   * 创建同步任务
   */
  private async createSyncTask(rule: SyncRule, options?: any): Promise<SyncTaskEntity> {
    const task = this.syncTaskRepository.create({
      syncRuleId: rule.id,
      systemId: rule.sourceSystem,
      entityType: rule.entityType,
      syncDirection: 'export',
      status: 'running',
      startTime: new Date(),
      processedCount: 0,
      errorCount: 0,
      syncOptions: options
    });

    return await this.syncTaskRepository.save(task);
  }

  /**
   * 完成同步任务
   */
  private async completeSyncTask(taskId: string, result: any): Promise<void> {
    await this.syncTaskRepository.update(taskId, {
      status: 'completed',
      endTime: new Date(),
      processedCount: result.processedCount,
      errorCount: result.errorCount,
      result: result
    });
  }

  /**
   * 失败同步任务
   */
  private async failSyncTask(taskId: string, errorMessage: string): Promise<void> {
    await this.syncTaskRepository.update(taskId, {
      status: 'failed',
      endTime: new Date(),
      errorMessage
    });
  }

  /**
   * 执行全量同步
   */
  private async executeFullSync(rule: SyncRule, taskId: string): Promise<any> {
    // 模拟全量同步实现
    return {
      processedCount: 1000,
      errorCount: 0,
      conflicts: 0,
      duration: 120000
    };
  }

  /**
   * 执行增量同步
   */
  private async executeIncrementalSync(rule: SyncRule, taskId: string): Promise<any> {
    // 模拟增量同步实现
    return {
      processedCount: 100,
      errorCount: 0,
      conflicts: 0,
      duration: 30000
    };
  }

  /**
   * 执行差异同步
   */
  private async executeDeltaSync(rule: SyncRule, taskId: string): Promise<any> {
    // 模拟差异同步实现
    return {
      processedCount: 50,
      errorCount: 0,
      conflicts: 2,
      duration: 15000
    };
  }

  /**
   * 执行实时同步
   */
  private async executeRealtimeSync(rule: SyncRule, taskId: string): Promise<any> {
    // 模拟实时同步实现
    return {
      processedCount: 1,
      errorCount: 0,
      conflicts: 0,
      duration: 1000
    };
  }

  /**
   * 查找同步规则
   */
  private findSyncRule(sourceSystem: string, targetSystem: string, entityType: string): SyncRule | undefined {
    return Array.from(this.syncRules.values()).find(rule =>
      rule.sourceSystem === sourceSystem &&
      rule.targetSystem === targetSystem &&
      rule.entityType === entityType
    );
  }

  /**
   * 数据转换
   */
  private async transformData(data: any, mappings: DataMapping[]): Promise<any> {
    const transformed: any = {};

    for (const mapping of mappings) {
      let value = data[mapping.sourceField];

      // 应用转换
      if (mapping.transformation) {
        value = await this.applyTransformation(value, mapping.transformation);
      }

      transformed[mapping.targetField] = value;
    }

    return transformed;
  }

  /**
   * 应用数据转换
   */
  private async applyTransformation(value: any, transformation: any): Promise<any> {
    switch (transformation.type) {
      case 'format':
        return this.formatValue(value, transformation.config);
      case 'lookup':
        return this.lookupValue(value, transformation.config);
      case 'calculation':
        return this.calculateValue(value, transformation.config);
      case 'custom':
        return this.customTransformation(value, transformation.config);
      default:
        return value;
    }
  }

  /**
   * 格式化值
   */
  private formatValue(value: any, config: any): any {
    // 简化的格式化实现
    return value;
  }

  /**
   * 查找值
   */
  private lookupValue(value: any, config: any): any {
    // 简化的查找实现
    return value;
  }

  /**
   * 计算值
   */
  private calculateValue(value: any, config: any): any {
    // 简化的计算实现
    return value;
  }

  /**
   * 自定义转换
   */
  private customTransformation(value: any, config: any): any {
    // 简化的自定义转换实现
    return value;
  }

  /**
   * 验证数据
   */
  private validateData(data: any, mappings: DataMapping[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const mapping of mappings) {
      if (mapping.validation) {
        const value = data[mapping.targetField];
        
        if (mapping.validation.required && (value === undefined || value === null)) {
          errors.push(`字段 ${mapping.targetField} 是必需的`);
        }

        // 其他验证逻辑...
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 推送到目标系统
   */
  private async pushToTargetSystem(targetSystem: string, entityType: string, data: any): Promise<boolean> {
    // 模拟推送实现
    return true;
  }

  /**
   * 应用冲突解决方案
   */
  private async applyConflictResolution(conflict: ConflictResolution): Promise<void> {
    // 模拟应用冲突解决方案
    this.logger.log(`应用冲突解决方案: ${conflict.recordId}`);
  }

  /**
   * 检查是否应该运行计划同步
   */
  private shouldRunScheduledSync(rule: SyncRule): boolean {
    // 简化的调度检查逻辑
    return true;
  }
}
