import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * 报表实体
 */
@Entity('reports')
@Index(['reportType', 'createdAt'])
@Index(['status', 'createdAt'])
export class ReportEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, comment: '报表类型' })
  reportType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';

  @Column({ type: 'varchar', length: 200, comment: '报表标题' })
  title: string;

  @Column({ type: 'text', nullable: true, comment: '报表描述' })
  description?: string;

  @Column({ type: 'timestamp', comment: '报表开始时间' })
  startDate: Date;

  @Column({ type: 'timestamp', comment: '报表结束时间' })
  endDate: Date;

  @Column({ type: 'json', comment: '报表数据' })
  data: any;

  @Column({ type: 'json', nullable: true, comment: 'KPI指标' })
  kpiMetrics?: any;

  @Column({ type: 'json', nullable: true, comment: '图表数据' })
  chartData?: any;

  @Column({ type: 'json', nullable: true, comment: '趋势分析' })
  trends?: any;

  @Column({ type: 'json', nullable: true, comment: '建议' })
  recommendations?: string[];

  @Column({ type: 'varchar', length: 50, comment: '报表格式' })
  format: 'json' | 'pdf' | 'excel' | 'html';

  @Column({ type: 'varchar', length: 50, comment: '状态' })
  status: 'pending' | 'generating' | 'completed' | 'failed';

  @Column({ type: 'text', nullable: true, comment: '文件路径' })
  filePath?: string;

  @Column({ type: 'bigint', nullable: true, comment: '文件大小(字节)' })
  fileSize?: number;

  @Column({ type: 'json', nullable: true, comment: '生成参数' })
  parameters?: any;

  @Column({ type: 'json', nullable: true, comment: '错误信息' })
  errors?: any;

  @Column({ type: 'varchar', length: 100, comment: '创建者' })
  createdBy: string;

  @Column({ type: 'timestamp', nullable: true, comment: '生成完成时间' })
  completedAt?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '过期时间' })
  expiresAt?: Date;

  @Column({ type: 'int', default: 0, comment: '下载次数' })
  downloadCount: number;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  notes?: string;
}
