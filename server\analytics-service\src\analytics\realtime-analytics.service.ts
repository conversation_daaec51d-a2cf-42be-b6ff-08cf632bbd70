import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { WebSocketGateway, WebSocketServer, SubscribeMessage, MessageBody } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import moment from 'moment';
import * as ss from 'simple-statistics';
import { ProductionDataEntity } from '../entities/production-data.entity';
import { AnalysisResultEntity } from '../entities/analysis-result.entity';

/**
 * 实时数据流接口
 */
interface RealtimeDataStream {
  deviceId: string;
  timestamp: Date;
  metrics: {
    [key: string]: number;
  };
  status: 'normal' | 'warning' | 'critical';
}

/**
 * 实时警报接口
 */
interface RealtimeAlert {
  id: string;
  deviceId: string;
  alertType: 'threshold' | 'anomaly' | 'prediction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

/**
 * 实时KPI接口
 */
interface RealtimeKPI {
  timestamp: Date;
  oee: number;
  availability: number;
  performance: number;
  quality: number;
  throughput: number;
  energyEfficiency: number;
}

/**
 * 实时分析服务
 */
@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/realtime-analytics'
})
export class RealtimeAnalyticsService {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RealtimeAnalyticsService.name);
  
  // 实时数据缓存
  private realtimeDataCache: Map<string, RealtimeDataStream[]> = new Map();
  private alertsCache: Map<string, RealtimeAlert> = new Map();
  private kpiCache: RealtimeKPI[] = [];
  
  // 连接的客户端
  private connectedClients: Set<Socket> = new Set();
  
  // 数据流配置
  private readonly maxCacheSize = 1000;
  private readonly alertRetentionTime = 24 * 60 * 60 * 1000; // 24小时

  constructor(
    @InjectRepository(ProductionDataEntity)
    private productionDataRepository: Repository<ProductionDataEntity>,
    @InjectRepository(AnalysisResultEntity)
    private analysisResultRepository: Repository<AnalysisResultEntity>
  ) {
    this.startRealtimeProcessing();
  }

  /**
   * 处理客户端连接
   */
  handleConnection(client: Socket): void {
    this.connectedClients.add(client);
    this.logger.log(`客户端连接: ${client.id}`);
    
    // 发送当前状态
    this.sendCurrentStatus(client);
  }

  /**
   * 处理客户端断开连接
   */
  handleDisconnect(client: Socket): void {
    this.connectedClients.delete(client);
    this.logger.log(`客户端断开连接: ${client.id}`);
  }

  /**
   * 订阅设备数据流
   */
  @SubscribeMessage('subscribe-device')
  handleSubscribeDevice(@MessageBody() deviceId: string, client: Socket): void {
    client.join(`device-${deviceId}`);
    this.logger.log(`客户端 ${client.id} 订阅设备 ${deviceId}`);
    
    // 发送设备历史数据
    const deviceData = this.realtimeDataCache.get(deviceId) || [];
    client.emit('device-history', {
      deviceId,
      data: deviceData.slice(-100) // 最近100条记录
    });
  }

  /**
   * 取消订阅设备数据流
   */
  @SubscribeMessage('unsubscribe-device')
  handleUnsubscribeDevice(@MessageBody() deviceId: string, client: Socket): void {
    client.leave(`device-${deviceId}`);
    this.logger.log(`客户端 ${client.id} 取消订阅设备 ${deviceId}`);
  }

  /**
   * 处理实时数据输入
   */
  async processRealtimeData(data: RealtimeDataStream): Promise<void> {
    try {
      // 缓存数据
      this.cacheRealtimeData(data);
      
      // 实时分析
      const analysis = await this.performRealtimeAnalysis(data);
      
      // 检查警报
      const alerts = await this.checkAlerts(data);
      
      // 广播数据
      this.broadcastRealtimeData(data, analysis, alerts);
      
      // 更新KPI
      await this.updateRealtimeKPI();
      
    } catch (error) {
      this.logger.error('处理实时数据失败', error);
    }
  }

  /**
   * 获取实时KPI
   */
  async getRealtimeKPI(): Promise<RealtimeKPI> {
    if (this.kpiCache.length > 0) {
      return this.kpiCache[this.kpiCache.length - 1];
    }
    
    // 计算当前KPI
    return await this.calculateCurrentKPI();
  }

  /**
   * 获取活跃警报
   */
  getActiveAlerts(): RealtimeAlert[] {
    const now = Date.now();
    return Array.from(this.alertsCache.values())
      .filter(alert => !alert.acknowledged && (now - alert.timestamp.getTime()) < this.alertRetentionTime)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 确认警报
   */
  async acknowledgeAlert(alertId: string): Promise<boolean> {
    const alert = this.alertsCache.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      this.server.emit('alert-acknowledged', { alertId, timestamp: new Date() });
      return true;
    }
    return false;
  }

  /**
   * 获取设备实时状态
   */
  getDeviceRealtimeStatus(deviceId: string): any {
    const deviceData = this.realtimeDataCache.get(deviceId);
    if (!deviceData || deviceData.length === 0) {
      return null;
    }
    
    const latestData = deviceData[deviceData.length - 1];
    const recentData = deviceData.slice(-10); // 最近10条记录
    
    return {
      deviceId,
      status: latestData.status,
      lastUpdate: latestData.timestamp,
      currentMetrics: latestData.metrics,
      trends: this.calculateShortTermTrends(recentData),
      alerts: this.getDeviceAlerts(deviceId)
    };
  }

  /**
   * 启动实时处理
   */
  private startRealtimeProcessing(): void {
    // 模拟数据生成（实际环境中应该从设备或消息队列接收）
    setInterval(() => {
      this.generateSimulatedData();
    }, 5000); // 每5秒生成一次数据
    
    this.logger.log('实时处理已启动');
  }

  /**
   * 生成模拟数据
   */
  private generateSimulatedData(): void {
    const devices = ['device-001', 'device-002', 'device-003', 'device-004'];
    
    devices.forEach(deviceId => {
      const data: RealtimeDataStream = {
        deviceId,
        timestamp: new Date(),
        metrics: {
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5,
          energyConsumption: 50 + Math.random() * 20
        },
        status: Math.random() > 0.9 ? 'warning' : 'normal'
      };
      
      this.processRealtimeData(data);
    });
  }

  /**
   * 缓存实时数据
   */
  private cacheRealtimeData(data: RealtimeDataStream): void {
    if (!this.realtimeDataCache.has(data.deviceId)) {
      this.realtimeDataCache.set(data.deviceId, []);
    }
    
    const deviceCache = this.realtimeDataCache.get(data.deviceId)!;
    deviceCache.push(data);
    
    // 限制缓存大小
    if (deviceCache.length > this.maxCacheSize) {
      deviceCache.shift();
    }
  }

  /**
   * 执行实时分析
   */
  private async performRealtimeAnalysis(data: RealtimeDataStream): Promise<any> {
    const deviceData = this.realtimeDataCache.get(data.deviceId) || [];
    
    if (deviceData.length < 2) {
      return null;
    }
    
    // 计算趋势
    const trends = this.calculateShortTermTrends(deviceData.slice(-10));
    
    // 异常检测
    const anomalies = this.detectRealtimeAnomalies(data, deviceData);
    
    return {
      deviceId: data.deviceId,
      timestamp: data.timestamp,
      trends,
      anomalies,
      predictions: this.generateShortTermPredictions(deviceData)
    };
  }

  /**
   * 检查警报
   */
  private async checkAlerts(data: RealtimeDataStream): Promise<RealtimeAlert[]> {
    const alerts: RealtimeAlert[] = [];
    
    // 阈值检查
    Object.entries(data.metrics).forEach(([metric, value]) => {
      const threshold = this.getMetricThreshold(metric);
      if (value > threshold.high) {
        alerts.push({
          id: `${data.deviceId}-${metric}-${Date.now()}`,
          deviceId: data.deviceId,
          alertType: 'threshold',
          severity: value > threshold.critical ? 'critical' : 'high',
          message: `${metric} 超过阈值: ${value.toFixed(2)} > ${threshold.high}`,
          timestamp: data.timestamp,
          acknowledged: false
        });
      }
    });
    
    // 缓存警报
    alerts.forEach(alert => {
      this.alertsCache.set(alert.id, alert);
    });
    
    return alerts;
  }

  /**
   * 广播实时数据
   */
  private broadcastRealtimeData(data: RealtimeDataStream, analysis: any, alerts: RealtimeAlert[]): void {
    // 广播给订阅该设备的客户端
    this.server.to(`device-${data.deviceId}`).emit('realtime-data', {
      data,
      analysis,
      alerts
    });
    
    // 广播警报给所有客户端
    if (alerts.length > 0) {
      this.server.emit('new-alerts', alerts);
    }
  }

  /**
   * 更新实时KPI
   */
  @Cron(CronExpression.EVERY_MINUTE)
  private async updateRealtimeKPI(): Promise<void> {
    try {
      const kpi = await this.calculateCurrentKPI();
      this.kpiCache.push(kpi);
      
      // 限制KPI缓存大小
      if (this.kpiCache.length > 1440) { // 保留24小时数据
        this.kpiCache.shift();
      }
      
      // 广播KPI更新
      this.server.emit('kpi-update', kpi);
      
    } catch (error) {
      this.logger.error('更新实时KPI失败', error);
    }
  }

  /**
   * 计算当前KPI
   */
  private async calculateCurrentKPI(): Promise<RealtimeKPI> {
    const allDeviceData: RealtimeDataStream[] = [];
    
    // 收集所有设备的最新数据
    this.realtimeDataCache.forEach(deviceData => {
      if (deviceData.length > 0) {
        allDeviceData.push(deviceData[deviceData.length - 1]);
      }
    });
    
    if (allDeviceData.length === 0) {
      return {
        timestamp: new Date(),
        oee: 0,
        availability: 0,
        performance: 0,
        quality: 0,
        throughput: 0,
        energyEfficiency: 0
      };
    }
    
    // 计算平均值
    const avgOutput = ss.mean(allDeviceData.map(d => d.metrics.output || 0));
    const avgQuality = ss.mean(allDeviceData.map(d => d.metrics.quality || 0));
    const avgEfficiency = ss.mean(allDeviceData.map(d => d.metrics.efficiency || 0));
    const totalEnergy = allDeviceData.reduce((sum, d) => sum + (d.metrics.energyConsumption || 0), 0);
    
    return {
      timestamp: new Date(),
      oee: (avgEfficiency * avgQuality) / 100,
      availability: allDeviceData.filter(d => d.status === 'normal').length / allDeviceData.length * 100,
      performance: avgEfficiency,
      quality: avgQuality,
      throughput: avgOutput,
      energyEfficiency: totalEnergy > 0 ? avgOutput / totalEnergy : 0
    };
  }

  /**
   * 发送当前状态给客户端
   */
  private sendCurrentStatus(client: Socket): void {
    const status = {
      timestamp: new Date(),
      connectedDevices: Array.from(this.realtimeDataCache.keys()),
      activeAlerts: this.getActiveAlerts().length,
      currentKPI: this.kpiCache.length > 0 ? this.kpiCache[this.kpiCache.length - 1] : null
    };
    
    client.emit('current-status', status);
  }

  /**
   * 计算短期趋势
   */
  private calculateShortTermTrends(data: RealtimeDataStream[]): any {
    if (data.length < 2) return {};
    
    const trends: any = {};
    const metrics = Object.keys(data[0].metrics);
    
    metrics.forEach(metric => {
      const values = data.map(d => d.metrics[metric]);
      const firstHalf = values.slice(0, Math.floor(values.length / 2));
      const secondHalf = values.slice(Math.floor(values.length / 2));
      
      const firstAvg = ss.mean(firstHalf);
      const secondAvg = ss.mean(secondHalf);
      const change = ((secondAvg - firstAvg) / firstAvg) * 100;
      
      trends[metric] = {
        direction: change > 2 ? 'increasing' : change < -2 ? 'decreasing' : 'stable',
        change: change,
        current: values[values.length - 1]
      };
    });
    
    return trends;
  }

  /**
   * 检测实时异常
   */
  private detectRealtimeAnomalies(current: RealtimeDataStream, history: RealtimeDataStream[]): any[] {
    const anomalies: any[] = [];
    
    if (history.length < 10) return anomalies;
    
    Object.entries(current.metrics).forEach(([metric, value]) => {
      const historicalValues = history.map(d => d.metrics[metric]);
      const mean = ss.mean(historicalValues);
      const stdDev = ss.standardDeviation(historicalValues);
      
      // Z-score异常检测
      const zScore = Math.abs((value - mean) / stdDev);
      if (zScore > 2.5) {
        anomalies.push({
          metric,
          value,
          zScore,
          severity: zScore > 3 ? 'high' : 'medium',
          description: `${metric} 异常: ${value.toFixed(2)} (Z-score: ${zScore.toFixed(2)})`
        });
      }
    });
    
    return anomalies;
  }

  /**
   * 生成短期预测
   */
  private generateShortTermPredictions(data: RealtimeDataStream[]): any {
    if (data.length < 5) return {};
    
    const predictions: any = {};
    const metrics = Object.keys(data[0].metrics);
    
    metrics.forEach(metric => {
      const values = data.map(d => d.metrics[metric]);
      const recentTrend = this.calculateLinearTrend(values.slice(-5));
      
      predictions[metric] = {
        next5min: values[values.length - 1] + recentTrend * 1,
        next10min: values[values.length - 1] + recentTrend * 2,
        confidence: Math.max(0, 100 - Math.abs(recentTrend) * 10)
      };
    });
    
    return predictions;
  }

  /**
   * 计算线性趋势
   */
  private calculateLinearTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  /**
   * 获取指标阈值
   */
  private getMetricThreshold(metric: string): { high: number; critical: number } {
    const thresholds: { [key: string]: { high: number; critical: number } } = {
      temperature: { high: 35, critical: 40 },
      pressure: { high: 120, critical: 130 },
      vibration: { high: 4, critical: 5 },
      output: { high: 150, critical: 180 },
      energyConsumption: { high: 80, critical: 100 }
    };
    
    return thresholds[metric] || { high: 100, critical: 120 };
  }

  /**
   * 获取设备警报
   */
  private getDeviceAlerts(deviceId: string): RealtimeAlert[] {
    return Array.from(this.alertsCache.values())
      .filter(alert => alert.deviceId === deviceId && !alert.acknowledged);
  }
}
