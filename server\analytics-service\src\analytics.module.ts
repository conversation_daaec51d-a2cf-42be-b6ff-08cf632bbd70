/**
 * 智慧工厂分析服务主模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';

// 分析模块
import { AnalyticsService } from './analytics/analytics.service';
import { RealtimeAnalyticsService } from './analytics/realtime-analytics.service';
import { VisualizationService } from './analytics/visualization.service';
import { ReportGenerationService } from './analytics/report-generation.service';
import { MachineLearningService } from './analytics/machine-learning.service';
import { AnalyticsController } from './analytics/analytics.controller';

// 实体
import { ProductionDataEntity } from './entities/production-data.entity';
import { AnalysisResultEntity } from './entities/analysis-result.entity';
import { ReportEntity } from './entities/report.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', ''),
        database: configService.get<string>('DB_DATABASE', 'smart_factory_analytics'),
        entities: [ProductionDataEntity, AnalysisResultEntity, ReportEntity],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
        logging: configService.get<boolean>('DB_LOGGING', false),
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        }
      }),
      inject: [ConfigService]
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // TypeORM 功能模块
    TypeOrmModule.forFeature([ProductionDataEntity, AnalysisResultEntity, ReportEntity]),
  ],

  controllers: [
    AnalyticsController
  ],

  providers: [
    AnalyticsService,
    RealtimeAnalyticsService,
    VisualizationService,
    ReportGenerationService,
    MachineLearningService,
    
    // 全局提供者
    {
      provide: 'APP_CONFIG',
      useFactory: (configService: ConfigService) => ({
        name: 'analytics-service',
        version: '1.0.0',
        environment: configService.get<string>('NODE_ENV', 'development'),
        port: configService.get<number>('PORT', 3007),
        debug: configService.get<boolean>('DEBUG', false)
      }),
      inject: [ConfigService]
    },

    // Redis 配置
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      }),
      inject: [ConfigService]
    }
  ],

  exports: [
    AnalyticsService,
    RealtimeAnalyticsService,
    VisualizationService,
    ReportGenerationService,
    MachineLearningService
  ]
})
export class AnalyticsModule {
  constructor(private configService: ConfigService) {
    // 模块初始化日志
    console.log('智慧工厂分析服务模块已加载');
    console.log(`环境: ${this.configService.get<string>('NODE_ENV', 'development')}`);
    console.log(`数据库: ${this.configService.get<string>('DB_HOST', 'localhost')}:${this.configService.get<number>('DB_PORT', 3306)}`);
    console.log(`Redis: ${this.configService.get<string>('REDIS_HOST', 'localhost')}:${this.configService.get<number>('REDIS_PORT', 6379)}`);
  }
}
