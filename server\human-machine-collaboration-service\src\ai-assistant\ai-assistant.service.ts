import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AIConversation } from '../database/entities/ai-conversation.entity';
import { NLPService } from './nlp.service';
import { KnowledgeBaseService } from './knowledge-base.service';

/**
 * AI智能助手服务
 * 提供智能对话、问答、建议等功能
 */
@Injectable()
export class AIAssistantService {
  private readonly logger = new Logger(AIAssistantService.name);

  constructor(
    @InjectRepository(AIConversation)
    private readonly conversationRepository: Repository<AIConversation>,
    private readonly nlpService: NLPService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理用户消息
   * @param userId 用户ID
   * @param message 用户消息
   * @param context 上下文信息
   * @returns AI响应
   */
  async processMessage(
    userId: string,
    message: string,
    context?: any,
  ): Promise<any> {
    try {
      const startTime = Date.now();

      // 1. 自然语言理解
      const nlpResult = await this.nlpService.analyzeMessage(message);

      // 2. 意图识别和实体提取
      const intent = await this.nlpService.extractIntent(message);
      const entities = await this.nlpService.extractEntities(message);

      // 3. 根据意图生成响应
      let response: string;
      let conversationType = 'general';

      switch (intent.name) {
        case 'maintenance_question':
          response = await this.handleMaintenanceQuestion(message, entities, context);
          conversationType = 'maintenance';
          break;
        case 'troubleshooting_request':
          response = await this.handleTroubleshootingRequest(message, entities, context);
          conversationType = 'troubleshooting';
          break;
        case 'training_inquiry':
          response = await this.handleTrainingInquiry(message, entities, context);
          conversationType = 'training';
          break;
        case 'general_support':
          response = await this.handleGeneralSupport(message, entities, context);
          conversationType = 'support';
          break;
        default:
          response = await this.handleGeneralConversation(message, context);
          break;
      }

      const responseTime = Date.now() - startTime;

      // 4. 保存对话记录
      const conversation = await this.saveConversation({
        userId,
        userMessage: message,
        aiResponse: response,
        context,
        intent: {
          name: intent.name,
          confidence: intent.confidence,
          entities,
        },
        confidence: intent.confidence,
        responseTime,
        conversationType,
      });

      return {
        id: conversation.id,
        response,
        intent: intent.name,
        confidence: intent.confidence,
        entities,
        responseTime,
        suggestions: await this.generateSuggestions(intent, entities, context),
      };

    } catch (error) {
      this.logger.error('处理用户消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理维护相关问题
   */
  private async handleMaintenanceQuestion(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      // 从知识库搜索相关维护信息
      const equipment = entities.find(e => e.type === 'equipment')?.value;
      const procedure = entities.find(e => e.type === 'procedure')?.value;

      const knowledgeResults = await this.knowledgeBaseService.searchMaintenance({
        equipment,
        procedure,
        query: message,
      });

      if (knowledgeResults.length > 0) {
        const bestMatch = knowledgeResults[0];
        return this.formatMaintenanceResponse(bestMatch);
      }

      return '抱歉，我没有找到相关的维护信息。请提供更具体的设备型号或维护步骤。';

    } catch (error) {
      this.logger.error('处理维护问题失败:', error);
      return '处理您的维护问题时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理故障排除请求
   */
  private async handleTroubleshootingRequest(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      const symptom = entities.find(e => e.type === 'symptom')?.value;
      const equipment = entities.find(e => e.type === 'equipment')?.value;

      const troubleshootingSteps = await this.knowledgeBaseService.searchTroubleshooting({
        symptom,
        equipment,
        query: message,
      });

      if (troubleshootingSteps.length > 0) {
        return this.formatTroubleshootingResponse(troubleshootingSteps);
      }

      return '请描述具体的故障现象，我会为您提供相应的排除步骤。';

    } catch (error) {
      this.logger.error('处理故障排除请求失败:', error);
      return '处理您的故障排除请求时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理培训咨询
   */
  private async handleTrainingInquiry(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      const skill = entities.find(e => e.type === 'skill')?.value;
      const level = entities.find(e => e.type === 'level')?.value;

      const trainingResources = await this.knowledgeBaseService.searchTraining({
        skill,
        level,
        query: message,
      });

      if (trainingResources.length > 0) {
        return this.formatTrainingResponse(trainingResources);
      }

      return '我可以为您推荐相关的培训资源。请告诉我您想学习的具体技能或操作。';

    } catch (error) {
      this.logger.error('处理培训咨询失败:', error);
      return '处理您的培训咨询时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理一般支持
   */
  private async handleGeneralSupport(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      // 搜索常见问题解答
      const faqResults = await this.knowledgeBaseService.searchFAQ(message);

      if (faqResults.length > 0) {
        return faqResults[0].answer;
      }

      return '我是您的智能助手，可以帮助您解决维护、故障排除、培训等问题。请告诉我您需要什么帮助。';

    } catch (error) {
      this.logger.error('处理一般支持失败:', error);
      return '处理您的请求时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理一般对话
   */
  private async handleGeneralConversation(
    message: string,
    context?: any,
  ): Promise<string> {
    // 简单的规则基础响应
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('你好') || lowerMessage.includes('hello')) {
      return '您好！我是您的智能助手，可以帮助您解决AR/VR维护指导、设备操作、故障排除等问题。有什么可以为您服务的吗？';
    }

    if (lowerMessage.includes('谢谢') || lowerMessage.includes('thank')) {
      return '不客气！如果您还有其他问题，随时可以问我。';
    }

    if (lowerMessage.includes('帮助') || lowerMessage.includes('help')) {
      return '我可以为您提供以下帮助：\n1. 设备维护指导\n2. 故障排除建议\n3. 操作培训资源\n4. AR/VR场景使用说明\n请告诉我您需要哪方面的帮助。';
    }

    return '我理解您的问题。请提供更多详细信息，这样我可以为您提供更准确的帮助。';
  }

  /**
   * 生成建议
   */
  private async generateSuggestions(
    intent: any,
    entities: any[],
    context?: any,
  ): Promise<string[]> {
    const suggestions: string[] = [];

    switch (intent.name) {
      case 'maintenance_question':
        suggestions.push(
          '查看维护手册',
          '观看操作视频',
          '联系技术支持',
        );
        break;
      case 'troubleshooting_request':
        suggestions.push(
          '运行诊断程序',
          '检查设备状态',
          '查看错误日志',
        );
        break;
      case 'training_inquiry':
        suggestions.push(
          '开始AR/VR培训',
          '查看培训计划',
          '预约实操训练',
        );
        break;
      default:
        suggestions.push(
          '我需要维护帮助',
          '设备出现故障',
          '我想学习新技能',
        );
        break;
    }

    return suggestions;
  }

  /**
   * 保存对话记录
   */
  private async saveConversation(data: any): Promise<AIConversation> {
    const conversation = this.conversationRepository.create(data);
    const saved = await this.conversationRepository.save(conversation);
    return Array.isArray(saved) ? saved[0] : saved;
  }

  /**
   * 格式化维护响应
   */
  private formatMaintenanceResponse(knowledgeItem: any): string {
    return `根据您的问题，我找到了相关的维护信息：\n\n${knowledgeItem.content}\n\n如果您需要更详细的指导，我可以为您启动AR/VR维护场景。`;
  }

  /**
   * 格式化故障排除响应
   */
  private formatTroubleshootingResponse(steps: any[]): string {
    let response = '请按照以下步骤进行故障排除：\n\n';
    steps.forEach((step, index) => {
      response += `${index + 1}. ${step.description}\n`;
    });
    response += '\n如果问题仍未解决，请联系技术支持。';
    return response;
  }

  /**
   * 格式化培训响应
   */
  private formatTrainingResponse(resources: any[]): string {
    let response = '为您推荐以下培训资源：\n\n';
    resources.forEach((resource, index) => {
      response += `${index + 1}. ${resource.title} - ${resource.description}\n`;
    });
    response += '\n您可以选择开始AR/VR培训场景进行实践学习。';
    return response;
  }

  /**
   * 获取对话历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 对话历史
   */
  async getConversationHistory(userId: string, limit: number = 10): Promise<AIConversation[]> {
    return await this.conversationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 更新对话反馈
   * @param conversationId 对话ID
   * @param feedback 反馈信息
   */
  async updateFeedback(conversationId: string, feedback: any): Promise<void> {
    await this.conversationRepository.update(conversationId, { feedback });
  }

  /**
   * 智能推荐系统
   */
  async getIntelligentRecommendations(userId: string, context?: any): Promise<any> {
    try {
      const userHistory = await this.getConversationHistory(userId, 20);
      const recommendations = [];

      // 基于历史对话分析用户偏好
      const intentFrequency = this.analyzeIntentFrequency(userHistory);
      const mostCommonIntent = Object.keys(intentFrequency).reduce((a, b) =>
        intentFrequency[a] > intentFrequency[b] ? a : b
      );

      // 根据最常见意图推荐相关内容
      switch (mostCommonIntent) {
        case 'maintenance_question':
          recommendations.push({
            type: 'maintenance_schedule',
            title: '维护计划提醒',
            description: '根据您的设备使用情况，建议制定定期维护计划',
            priority: 'high'
          });
          break;
        case 'troubleshooting_request':
          recommendations.push({
            type: 'preventive_maintenance',
            title: '预防性维护',
            description: '建议进行预防性维护以减少故障发生',
            priority: 'medium'
          });
          break;
        case 'training_inquiry':
          recommendations.push({
            type: 'advanced_training',
            title: '进阶培训课程',
            description: '推荐适合您当前技能水平的进阶培训',
            priority: 'medium'
          });
          break;
      }

      // 基于上下文的推荐
      if (context?.currentEquipment) {
        recommendations.push({
          type: 'equipment_optimization',
          title: '设备优化建议',
          description: `针对${context.currentEquipment}的性能优化建议`,
          priority: 'low'
        });
      }

      return {
        success: true,
        recommendations: recommendations.slice(0, 5),
        userProfile: {
          mostCommonIntent,
          totalInteractions: userHistory.length,
          averageConfidence: this.calculateAverageConfidence(userHistory)
        }
      };

    } catch (error) {
      this.logger.error('获取智能推荐失败:', error);
      throw error;
    }
  }

  /**
   * 分析意图频率
   */
  private analyzeIntentFrequency(conversations: AIConversation[]): Record<string, number> {
    const frequency: Record<string, number> = {};

    conversations.forEach(conv => {
      const intent = conv.intent?.name || 'unknown';
      frequency[intent] = (frequency[intent] || 0) + 1;
    });

    return frequency;
  }

  /**
   * 计算平均置信度
   */
  private calculateAverageConfidence(conversations: AIConversation[]): number {
    if (conversations.length === 0) return 0;

    const totalConfidence = conversations.reduce((sum, conv) =>
      sum + (conv.confidence || 0), 0
    );

    return totalConfidence / conversations.length;
  }

  /**
   * 智能学习和适应
   */
  async learnFromFeedback(conversationId: string, feedback: any): Promise<any> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId }
      });

      if (!conversation) {
        throw new Error('对话记录不存在');
      }

      // 更新反馈
      await this.updateFeedback(conversationId, feedback);

      // 基于反馈调整AI模型（简化实现）
      if (feedback.rating < 3) {
        // 负面反馈，记录改进点
        this.logger.warn(`收到负面反馈: ${conversationId}`, feedback);

        // 实际应用中可以：
        // 1. 调整意图识别模型
        // 2. 更新知识库内容
        // 3. 优化响应生成策略
      } else if (feedback.rating >= 4) {
        // 正面反馈，强化成功模式
        this.logger.log(`收到正面反馈: ${conversationId}`, feedback);
      }

      return {
        success: true,
        message: '反馈已记录，AI将持续学习改进'
      };

    } catch (error) {
      this.logger.error('学习反馈失败:', error);
      throw error;
    }
  }

  /**
   * 多模态交互支持
   */
  async processMultimodalInput(
    userId: string,
    inputs: {
      text?: string;
      voice?: Buffer;
      image?: Buffer;
      gesture?: any;
    },
    context?: any
  ): Promise<any> {
    try {
      const results = {
        text: null,
        voice: null,
        image: null,
        gesture: null,
        combinedResponse: null
      };

      // 处理文本输入
      if (inputs.text) {
        results.text = await this.processMessage(userId, inputs.text, context);
      }

      // 处理语音输入（需要语音识别服务）
      if (inputs.voice) {
        // 这里应该调用语音识别服务
        // const speechText = await this.speechRecognitionService.recognize(inputs.voice);
        // results.voice = await this.processMessage(userId, speechText, context);
        results.voice = { message: '语音输入处理功能开发中' };
      }

      // 处理图像输入（需要图像识别服务）
      if (inputs.image) {
        // 这里应该调用图像识别服务
        // const imageAnalysis = await this.imageRecognitionService.analyze(inputs.image);
        // results.image = await this.processImageQuery(userId, imageAnalysis, context);
        results.image = { message: '图像输入处理功能开发中' };
      }

      // 处理手势输入
      if (inputs.gesture) {
        results.gesture = await this.processGestureInput(userId, inputs.gesture, context);
      }

      // 综合多模态输入生成响应
      results.combinedResponse = await this.generateMultimodalResponse(results, context);

      return {
        success: true,
        results,
        processingTime: Date.now()
      };

    } catch (error) {
      this.logger.error('处理多模态输入失败:', error);
      throw error;
    }
  }

  /**
   * 处理手势输入
   */
  private async processGestureInput(userId: string, gesture: any, context?: any): Promise<any> {
    try {
      // 简化的手势识别逻辑
      const gestureCommands = {
        'point': '指向操作',
        'grab': '抓取操作',
        'swipe': '滑动操作',
        'tap': '点击操作'
      };

      const command = gestureCommands[gesture.type] || '未知手势';

      return {
        recognizedGesture: gesture.type,
        command,
        confidence: gesture.confidence || 0.8,
        response: `识别到${command}，请继续您的操作`
      };

    } catch (error) {
      this.logger.error('处理手势输入失败:', error);
      return {
        error: '手势识别失败',
        response: '请重新尝试手势操作'
      };
    }
  }

  /**
   * 生成多模态响应
   */
  private async generateMultimodalResponse(results: any, context?: any): Promise<any> {
    try {
      const responses = [];

      if (results.text?.response) {
        responses.push(results.text.response);
      }

      if (results.voice?.response) {
        responses.push(results.voice.response);
      }

      if (results.gesture?.response) {
        responses.push(results.gesture.response);
      }

      if (responses.length === 0) {
        return {
          response: '我没有理解您的输入，请重新尝试',
          confidence: 0.1
        };
      }

      // 简化的响应合并逻辑
      const combinedResponse = responses.join(' ');
      const averageConfidence = results.text?.confidence || 0.7;

      return {
        response: combinedResponse,
        confidence: averageConfidence,
        modalitiesUsed: Object.keys(results).filter(key =>
          results[key] && key !== 'combinedResponse'
        )
      };

    } catch (error) {
      this.logger.error('生成多模态响应失败:', error);
      return {
        response: '处理您的输入时出现错误',
        confidence: 0.1
      };
    }
  }

  /**
   * 获取AI助手统计信息
   */
  async getStatistics(): Promise<any> {
    const totalConversations = await this.conversationRepository.count();
    const avgResponseTime = await this.conversationRepository
      .createQueryBuilder('conversation')
      .select('AVG(conversation.responseTime)', 'avgResponseTime')
      .getRawOne();

    const intentDistribution = await this.conversationRepository
      .createQueryBuilder('conversation')
      .select('conversation.intent', 'intent')
      .addSelect('COUNT(*)', 'count')
      .groupBy('conversation.intent')
      .getRawMany();

    return {
      totalConversations,
      averageResponseTime: avgResponseTime?.avgResponseTime || 0,
      intentDistribution,
      systemHealth: {
        status: 'healthy',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      }
    };
  }
}
