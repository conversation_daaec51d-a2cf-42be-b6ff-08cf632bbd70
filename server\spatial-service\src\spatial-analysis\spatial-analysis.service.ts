/**
 * 空间分析主服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { ProximityAnalysisDto, HotspotAnalysisDto, InterpolationDto } from './dto/analysis.dto';
import * as turf from '@turf/turf';

@Injectable()
export class SpatialAnalysisService {
  private readonly logger = new Logger(SpatialAnalysisService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
  ) {}

  /**
   * 邻近分析
   */
  async performProximityAnalysis(proximityAnalysisDto: ProximityAnalysisDto) {
    const startTime = Date.now();
    this.logger.log(`开始邻近分析: ${JSON.stringify(proximityAnalysisDto)}`);

    try {
      // 获取目标图层和参考图层
      const [targetFeatures, referenceFeatures] = await Promise.all([
        this.spatialFeatureRepository.find({
          where: { layerId: proximityAnalysisDto.targetLayerId }
        }),
        this.spatialFeatureRepository.find({
          where: { layerId: proximityAnalysisDto.referenceLayerId }
        })
      ]);

      const results = [];

      for (const targetFeature of targetFeatures) {
        const targetGeometry = JSON.parse(targetFeature.geometry);
        const neighbors = [];

        for (const refFeature of referenceFeatures) {
          const refGeometry = JSON.parse(refFeature.geometry);
          
          // 计算距离
          const distance = turf.distance(
            turf.centroid(targetGeometry),
            turf.centroid(refGeometry),
            { units: proximityAnalysisDto.unit as any }
          );

          if (distance <= proximityAnalysisDto.searchRadius) {
            neighbors.push({
              featureId: refFeature.id,
              distance: distance,
              properties: refFeature.properties
            });
          }
        }

        // 按距离排序并限制数量
        neighbors.sort((a, b) => a.distance - b.distance);
        const limitedNeighbors = neighbors.slice(0, proximityAnalysisDto.maxNeighbors);

        results.push({
          targetFeatureId: targetFeature.id,
          neighbors: limitedNeighbors,
          neighborCount: limitedNeighbors.length,
          averageDistance: limitedNeighbors.length > 0 
            ? limitedNeighbors.reduce((sum, n) => sum + n.distance, 0) / limitedNeighbors.length 
            : 0
        });
      }

      const processingTime = Date.now() - startTime;
      this.logger.log(`邻近分析完成，耗时: ${processingTime}ms`);

      return {
        success: true,
        results: results,
        processingTime: `${processingTime}ms`,
        parameters: proximityAnalysisDto,
        statistics: this.calculateProximityStatistics(results)
      };

    } catch (error) {
      this.logger.error('邻近分析失败:', error);
      throw new Error(`邻近分析失败: ${error.message}`);
    }
  }

  /**
   * 热点分析
   */
  async performHotspotAnalysis(hotspotAnalysisDto: HotspotAnalysisDto) {
    const startTime = Date.now();
    this.logger.log(`开始热点分析: ${JSON.stringify(hotspotAnalysisDto)}`);

    try {
      // 获取输入要素
      const features = await this.spatialFeatureRepository.find({
        where: { layerId: hotspotAnalysisDto.inputLayerId }
      });

      if (features.length === 0) {
        throw new Error('输入图层没有要素');
      }

      // 提取分析值
      const points = features.map(feature => {
        const geometry = JSON.parse(feature.geometry);
        const centroid = turf.centroid(geometry);
        const value = feature.properties?.[hotspotAnalysisDto.analysisField] || 0;
        
        return {
          ...centroid,
          properties: {
            ...feature.properties,
            analysisValue: value,
            featureId: feature.id
          }
        };
      });

      // 计算热点统计
      const hotspots = this.calculateHotspots(points, hotspotAnalysisDto);

      const processingTime = Date.now() - startTime;
      this.logger.log(`热点分析完成，耗时: ${processingTime}ms`);

      return {
        success: true,
        hotspots: hotspots,
        processingTime: `${processingTime}ms`,
        parameters: hotspotAnalysisDto,
        statistics: this.calculateHotspotStatistics(hotspots)
      };

    } catch (error) {
      this.logger.error('热点分析失败:', error);
      throw new Error(`热点分析失败: ${error.message}`);
    }
  }

  /**
   * 空间插值
   */
  async performInterpolation(interpolationDto: InterpolationDto) {
    const startTime = Date.now();
    this.logger.log(`开始空间插值: ${JSON.stringify(interpolationDto)}`);

    try {
      // 获取输入点要素
      const features = await this.spatialFeatureRepository.find({
        where: { layerId: interpolationDto.inputLayerId }
      });

      if (features.length < interpolationDto.minNeighbors) {
        throw new Error(`输入点数量不足，至少需要 ${interpolationDto.minNeighbors} 个点`);
      }

      // 提取插值点
      const points = features.map(feature => {
        const geometry = JSON.parse(feature.geometry);
        const value = feature.properties?.[interpolationDto.valueField];
        
        if (typeof value !== 'number') {
          throw new Error(`要素 ${feature.id} 的插值字段值无效`);
        }

        return turf.point(geometry.coordinates, { value });
      });

      // 执行插值
      const interpolationResult = this.performInterpolationMethod(points, interpolationDto);

      const processingTime = Date.now() - startTime;
      this.logger.log(`空间插值完成，耗时: ${processingTime}ms`);

      return {
        success: true,
        result: interpolationResult,
        processingTime: `${processingTime}ms`,
        parameters: interpolationDto,
        statistics: this.calculateInterpolationStatistics(interpolationResult)
      };

    } catch (error) {
      this.logger.error('空间插值失败:', error);
      throw new Error(`空间插值失败: ${error.message}`);
    }
  }

  /**
   * 缓冲区分析
   */
  async bufferAnalysis(featureId: string, distance: number, unit: string = 'meters') {
    this.logger.log(`执行缓冲区分析: ${featureId}, 距离: ${distance}${unit}`);

    const feature = await this.spatialFeatureRepository.findOne({
      where: { id: featureId }
    });

    if (!feature) {
      throw new Error('要素不存在');
    }

    const geometry = JSON.parse(feature.geometry);
    const buffered = turf.buffer(geometry, distance, { units: unit as any });

    return {
      success: true,
      result: buffered,
      originalFeature: feature,
      parameters: { distance, unit },
      area: turf.area(buffered)
    };
  }

  /**
   * 多要素缓冲区分析
   */
  async multiBufferAnalysis(featureIds: string[], distance: number, unit: string = 'meters') {
    this.logger.log(`执行多要素缓冲区分析: ${featureIds.length}个要素`);

    const features = await this.spatialFeatureRepository.findByIds(featureIds);
    if (features.length === 0) {
      throw new Error('未找到要素');
    }

    const results = [];
    for (const feature of features) {
      const geometry = JSON.parse(feature.geometry);
      const buffer = turf.buffer(geometry, distance, { units: unit as any });
      results.push({
        featureId: feature.id,
        buffer: buffer,
        area: turf.area(buffer)
      });
    }

    // 合并所有缓冲区
    const allBuffers = results.map(r => r.buffer);
    const union = allBuffers.reduce((acc, buffer) => {
      return acc ? turf.union(acc, buffer) : buffer;
    }, null);

    return {
      success: true,
      individualBuffers: results,
      unionBuffer: union,
      totalArea: union ? turf.area(union) : 0,
      parameters: { distance, unit, featureCount: features.length }
    };
  }

  /**
   * 相交分析
   */
  async intersectionAnalysis(featureId1: string, featureId2: string) {
    this.logger.log(`执行相交分析: ${featureId1} 与 ${featureId2}`);

    const [feature1, feature2] = await Promise.all([
      this.spatialFeatureRepository.findOne({ where: { id: featureId1 } }),
      this.spatialFeatureRepository.findOne({ where: { id: featureId2 } })
    ]);

    if (!feature1 || !feature2) {
      throw new Error('要素不存在');
    }

    const geometry1 = JSON.parse(feature1.geometry);
    const geometry2 = JSON.parse(feature2.geometry);
    const intersection = turf.intersect(geometry1, geometry2);

    return {
      success: true,
      result: intersection,
      features: [feature1, feature2],
      area: intersection ? turf.area(intersection) : 0,
      hasIntersection: intersection !== null
    };
  }

  /**
   * 联合分析
   */
  async unionAnalysis(featureIds: string[]) {
    this.logger.log(`执行联合分析: ${featureIds.length}个要素`);

    const features = await this.spatialFeatureRepository.findByIds(featureIds);
    if (features.length < 2) {
      throw new Error('联合分析至少需要2个要素');
    }

    const geometries = features.map(f => JSON.parse(f.geometry));
    const union = geometries.reduce((acc, geometry) => {
      return acc ? turf.union(acc, geometry) : geometry;
    }, null);

    return {
      success: true,
      result: union,
      originalFeatures: features,
      area: union ? turf.area(union) : 0
    };
  }

  /**
   * 差异分析
   */
  async differenceAnalysis(featureId1: string, featureId2: string) {
    this.logger.log(`执行差异分析: ${featureId1} - ${featureId2}`);

    const [feature1, feature2] = await Promise.all([
      this.spatialFeatureRepository.findOne({ where: { id: featureId1 } }),
      this.spatialFeatureRepository.findOne({ where: { id: featureId2 } })
    ]);

    if (!feature1 || !feature2) {
      throw new Error('要素不存在');
    }

    const geometry1 = JSON.parse(feature1.geometry);
    const geometry2 = JSON.parse(feature2.geometry);
    const difference = turf.difference(geometry1, geometry2);

    return {
      success: true,
      result: difference,
      features: [feature1, feature2],
      area: difference ? turf.area(difference) : 0
    };
  }

  /**
   * 距离计算
   */
  async calculateDistance(featureId1: string, featureId2: string, unit: string = 'meters') {
    this.logger.log(`计算距离: ${featureId1} 到 ${featureId2}`);

    const [feature1, feature2] = await Promise.all([
      this.spatialFeatureRepository.findOne({ where: { id: featureId1 } }),
      this.spatialFeatureRepository.findOne({ where: { id: featureId2 } })
    ]);

    if (!feature1 || !feature2) {
      throw new Error('要素不存在');
    }

    const geometry1 = JSON.parse(feature1.geometry);
    const geometry2 = JSON.parse(feature2.geometry);
    const distance = turf.distance(geometry1, geometry2, { units: unit as any });

    return {
      success: true,
      distance,
      unit,
      features: [feature1, feature2]
    };
  }

  /**
   * 获取分析能力
   */
  getAnalysisCapabilities() {
    return {
      spatialAnalysis: {
        buffer: {
          available: true,
          types: ['fixed', 'variable', 'multiple'],
          units: ['meters', 'kilometers', 'feet', 'miles']
        },
        overlay: {
          available: true,
          operations: ['intersection', 'union', 'difference', 'symmetric_difference', 'clip']
        },
        network: {
          available: true,
          types: ['shortest_path', 'service_area', 'closest_facility', 'route_optimization']
        },
        statistical: {
          available: true,
          statistics: ['count', 'sum', 'mean', 'min', 'max', 'std', 'variance']
        },
        proximity: {
          available: true,
          units: ['meters', 'kilometers', 'feet', 'miles']
        },
        hotspot: {
          available: true,
          methods: ['getis_ord', 'local_moran']
        },
        interpolation: {
          available: true,
          methods: ['idw', 'kriging', 'spline', 'natural_neighbor']
        }
      },
      geometryTypes: ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'],
      coordinateSystems: ['EPSG:4326', 'EPSG:3857', 'EPSG:4490'],
      outputFormats: ['geojson', 'shapefile', 'kml', 'csv']
    };
  }

  /**
   * 获取分析历史
   */
  async getAnalysisHistory(projectId: string, page: number, limit: number) {
    // 这里应该从数据库中获取分析历史记录
    // 简化实现，返回模拟数据
    return {
      analyses: [],
      total: 0,
      page,
      limit,
      totalPages: 0
    };
  }

  /**
   * 获取分析结果
   */
  async getAnalysisResult(analysisId: string) {
    // 这里应该从数据库中获取具体的分析结果
    // 简化实现
    return {
      id: analysisId,
      status: 'completed',
      result: null
    };
  }

  // 私有辅助方法

  private calculateProximityStatistics(results: any[]) {
    const allDistances = results.flatMap(r => r.neighbors.map(n => n.distance));
    
    return {
      totalTargets: results.length,
      totalNeighbors: allDistances.length,
      averageDistance: allDistances.length > 0 
        ? allDistances.reduce((sum, d) => sum + d, 0) / allDistances.length 
        : 0,
      minDistance: allDistances.length > 0 ? Math.min(...allDistances) : 0,
      maxDistance: allDistances.length > 0 ? Math.max(...allDistances) : 0
    };
  }

  private calculateHotspots(points: any[], params: HotspotAnalysisDto) {
    // 简化的热点计算实现
    // 实际应用中应该使用更复杂的统计方法
    const hotspots = [];
    
    for (const point of points) {
      const neighbors = points.filter(p => {
        const distance = turf.distance(point, p, { units: 'meters' });
        return distance <= (params.neighborhoodDistance || 1000);
      });

      const localSum = neighbors.reduce((sum, n) => sum + n.properties.analysisValue, 0);
      const localMean = localSum / neighbors.length;
      
      // 简化的Z分数计算
      const globalMean = points.reduce((sum, p) => sum + p.properties.analysisValue, 0) / points.length;
      const zScore = (localMean - globalMean) / Math.sqrt(globalMean);

      hotspots.push({
        ...point,
        properties: {
          ...point.properties,
          localSum,
          localMean,
          zScore,
          pValue: this.calculatePValue(zScore),
          significance: Math.abs(zScore) > 1.96 ? 'significant' : 'not_significant',
          hotspotType: zScore > 1.96 ? 'hot' : zScore < -1.96 ? 'cold' : 'neutral'
        }
      });
    }

    return hotspots;
  }

  private calculatePValue(zScore: number): number {
    // 简化的p值计算
    return 2 * (1 - this.normalCDF(Math.abs(zScore)));
  }

  private normalCDF(x: number): number {
    // 简化的标准正态分布累积分布函数
    return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
  }

  private erf(x: number): number {
    // 简化的误差函数实现
    const a1 =  0.254829592;
    const a2 = -0.284496736;
    const a3 =  1.421413741;
    const a4 = -1.453152027;
    const a5 =  1.061405429;
    const p  =  0.3275911;

    const sign = x >= 0 ? 1 : -1;
    x = Math.abs(x);

    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

    return sign * y;
  }

  private calculateHotspotStatistics(hotspots: any[]) {
    const hotCount = hotspots.filter(h => h.properties.hotspotType === 'hot').length;
    const coldCount = hotspots.filter(h => h.properties.hotspotType === 'cold').length;
    const neutralCount = hotspots.filter(h => h.properties.hotspotType === 'neutral').length;
    const significantCount = hotspots.filter(h => h.properties.significance === 'significant').length;

    return {
      totalPoints: hotspots.length,
      hotSpots: hotCount,
      coldSpots: coldCount,
      neutralSpots: neutralCount,
      significantSpots: significantCount,
      significanceRate: significantCount / hotspots.length
    };
  }

  private performInterpolationMethod(points: any[], params: InterpolationDto) {
    // 简化的插值实现
    // 实际应用中应该使用专业的插值算法
    switch (params.method) {
      case 'idw':
        return this.performIDW(points, params);
      default:
        throw new Error(`不支持的插值方法: ${params.method}`);
    }
  }

  private performIDW(points: any[], params: InterpolationDto) {
    // 简化的反距离权重插值实现
    const bbox = turf.bbox(turf.featureCollection(points));
    const grid = turf.pointGrid(bbox, params.cellSize, { units: 'meters' });

    const interpolatedGrid = grid.features.map(gridPoint => {
      let weightedSum = 0;
      let weightSum = 0;

      for (const point of points) {
        const distance = turf.distance(gridPoint, point, { units: 'meters' });
        
        if (distance === 0) {
          // 如果距离为0，直接使用该点的值
          return {
            ...gridPoint,
            properties: {
              ...gridPoint.properties,
              interpolatedValue: point.properties.value
            }
          };
        }

        const weight = 1 / Math.pow(distance, params.power);
        weightedSum += weight * point.properties.value;
        weightSum += weight;
      }

      const interpolatedValue = weightSum > 0 ? weightedSum / weightSum : 0;

      return {
        ...gridPoint,
        properties: {
          ...gridPoint.properties,
          interpolatedValue
        }
      };
    });

    return {
      type: 'FeatureCollection',
      features: interpolatedGrid
    };
  }

  private calculateInterpolationStatistics(result: any) {
    const values = result.features.map(f => f.properties.interpolatedValue);
    
    return {
      pointCount: result.features.length,
      minValue: Math.min(...values),
      maxValue: Math.max(...values),
      meanValue: values.reduce((sum, v) => sum + v, 0) / values.length,
      standardDeviation: this.calculateStandardDeviation(values)
    };
  }

  private calculateStandardDeviation(values: number[]): number {
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const squaredDiffs = values.map(v => Math.pow(v - mean, 2));
    const variance = squaredDiffs.reduce((sum, d) => sum + d, 0) / values.length;
    return Math.sqrt(variance);
  }
}
