import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * 分析结果实体
 */
@Entity('analysis_results')
@Index(['analysisType', 'createdAt'])
@Index(['deviceId', 'createdAt'])
export class AnalysisResultEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, comment: '分析类型' })
  analysisType: string;

  @Column({ type: 'varchar', length: 200, comment: '分析标题' })
  title: string;

  @Column({ type: 'text', comment: '分析描述' })
  description: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '设备ID' })
  deviceId?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '生产线' })
  productionLine?: string;

  @Column({ type: 'json', comment: '分析数据' })
  data: any;

  @Column({ type: 'json', comment: '洞察信息' })
  insights: string[];

  @Column({ type: 'json', comment: '建议' })
  recommendations: string[];

  @Column({ type: 'decimal', precision: 5, scale: 2, comment: '置信度' })
  confidence: number;

  @Column({ type: 'varchar', length: 50, comment: '严重程度' })
  severity: 'low' | 'medium' | 'high';

  @Column({ type: 'varchar', length: 50, comment: '状态' })
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @Column({ type: 'json', nullable: true, comment: '元数据' })
  metadata?: any;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '分析时间' })
  analyzedAt?: Date;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '创建者' })
  createdBy?: string;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  notes?: string;
}
